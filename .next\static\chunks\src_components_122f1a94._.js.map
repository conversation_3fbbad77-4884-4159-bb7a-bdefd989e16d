{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,OAAO,kBACX,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,6LAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC;oBAAG,WAAU;;wBAAmC;sCACvC,6LAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;KA7E9C;AAmFN,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;;;;kDACD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;MA1EM;AA4ES,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,6LAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E;GAhFwB;MAAA", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;KAXD;AAgBN,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;MAPC;AAWS,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,6LAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,6LAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,6LAAC;;sEACC,6LAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,6LAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,6LAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,6LAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,6LAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,6LAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,6LAAC;4CAAG,WAAU;;gDAAkC;8DACtC,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,6LAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,6LAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;MAlLwB", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/DemoEngine.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from '../ui/Button';\n\nexport default function DemoEngine() {\n  const [currentLevel, setCurrentLevel] = useState(1);\n  const [completedLevels, setCompletedLevels] = useState(new Set());\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [demoData, setDemoData] = useState({\n    userName: '<PERSON>',\n    userTitle: 'Senior Developer',\n    userCompany: 'TechCorp Inc.',\n    userEmail: '<EMAIL>',\n    userPhone: '+****************',\n    cardColor: 'cyan',\n    scanCount: 0,\n    shareCount: 0\n  });\n\n  const levels = [\n    {\n      id: 1,\n      title: 'Basic AR Card',\n      description: 'View your first 3D business card',\n      instruction: 'Welcome! This is your AR business card. Click on it to see the 3D effect in action.',\n    },\n    {\n      id: 2,\n      title: 'AR Camera View',\n      description: 'See how it looks in AR camera mode',\n      instruction: 'Now let\\'s see how your card appears through AR camera. This simulates the real-world experience.',\n    },\n    {\n      id: 3,\n      title: 'Customize Your Card',\n      description: 'Change colors, effects, and information',\n      instruction: 'Customize your card! Try different colors and effects.',\n    },\n    {\n      id: 4,\n      title: 'Sharing Methods',\n      description: 'Generate QR codes and sharing links',\n      instruction: 'Learn how to share your card. Generate QR codes and direct links.',\n    },\n    {\n      id: 5,\n      title: 'Analytics Dashboard',\n      description: 'Track scans and engagement',\n      instruction: 'See how your networking performs with real-time analytics.',\n    }\n  ];\n\n  const currentLevelData = levels.find(level => level.id === currentLevel);\n\n  const completeLevel = (levelId) => {\n    setCompletedLevels(prev => new Set([...prev, levelId]));\n    if (levelId === currentLevel && levelId < levels.length) {\n      setTimeout(() => setCurrentLevel(levelId + 1), 1000);\n    }\n  };\n\n  const resetDemo = () => {\n    setCurrentLevel(1);\n    setCompletedLevels(new Set());\n    setShowInstructions(true);\n    setDemoData(prev => ({\n      ...prev,\n      scanCount: 0,\n      shareCount: 0\n    }));\n  };\n\n  // Simple AR Card Component\n  const ARCard = ({ data, onInteraction }) => {\n    const [isHovered, setIsHovered] = useState(false);\n    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n    const cardRef = useRef(null);\n\n    const handleMouseMove = (e) => {\n      if (cardRef.current) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        \n        setMousePosition({\n          x: (e.clientX - centerX) / 15,\n          y: (e.clientY - centerY) / 15\n        });\n      }\n    };\n\n    const theme = {\n      cyan: '#00f5ff',\n      purple: '#8b5cf6',\n      green: '#10b981',\n      orange: '#f59e0b'\n    };\n\n    const cardColor = theme[data.cardColor] || theme.cyan;\n\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px] relative\">\n        <motion.div\n          ref={cardRef}\n          className=\"relative w-80 h-48 cursor-pointer\"\n          style={{\n            transform: `perspective(1000px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg)`,\n            transformStyle: 'preserve-3d'\n          }}\n          onMouseMove={handleMouseMove}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => {\n            setIsHovered(false);\n            setMousePosition({ x: 0, y: 0 });\n          }}\n          onClick={() => onInteraction?.()}\n          whileHover={{ scale: 1.05 }}\n          animate={{\n            boxShadow: [`0 0 20px ${cardColor}40`, `0 0 30px ${cardColor}60`, `0 0 20px ${cardColor}40`]\n          }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div \n            className=\"absolute inset-0 rounded-xl shadow-2xl border-2 overflow-hidden\"\n            style={{\n              background: `linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%)`,\n              borderColor: cardColor,\n              boxShadow: isHovered ? `0 0 30px ${cardColor}40` : '0 10px 30px rgba(0,0,0,0.3)'\n            }}\n          >\n            {/* AR Corner Indicators */}\n            <div className=\"absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 animate-pulse\" style={{ borderColor: cardColor }} />\n            <div className=\"absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 animate-pulse\" style={{ borderColor: cardColor }} />\n            <div className=\"absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 animate-pulse\" style={{ borderColor: cardColor }} />\n            <div className=\"absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 animate-pulse\" style={{ borderColor: cardColor }} />\n\n            {/* Card Content */}\n            <div className=\"p-6 h-full flex flex-col justify-between relative z-10\">\n              <div className=\"flex items-start justify-between\">\n                <div>\n                  <motion.div\n                    className=\"w-12 h-12 rounded-full mb-3 flex items-center justify-center text-lg font-bold text-black\"\n                    style={{ background: cardColor }}\n                    animate={{ rotate: isHovered ? 360 : 0 }}\n                    transition={{ duration: 1 }}\n                  >\n                    {data.userName.split(' ').map(n => n[0]).join('')}\n                  </motion.div>\n                  \n                  <h3 className=\"text-lg font-bold text-white mb-1\">{data.userName}</h3>\n                  <p className=\"text-gray-300 text-sm mb-1\">{data.userTitle}</p>\n                  <p className=\"text-gray-400 text-xs\">{data.userCompany}</p>\n                </div>\n\n                <div \n                  className=\"w-10 h-10 rounded-lg flex items-center justify-center text-xs font-bold\"\n                  style={{ background: cardColor + '20', color: cardColor }}\n                >\n                  AR\n                </div>\n              </div>\n\n              <div className=\"space-y-1\">\n                <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                  <span style={{ color: cardColor }}>📧</span>\n                  <span className=\"text-xs\">{data.userEmail}</span>\n                </div>\n                <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                  <span style={{ color: cardColor }}>📱</span>\n                  <span className=\"text-xs\">{data.userPhone}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Holographic Effect */}\n            {isHovered && (\n              <motion.div\n                className=\"absolute inset-0 rounded-xl pointer-events-none\"\n                style={{\n                  background: `linear-gradient(45deg, transparent 30%, ${cardColor}10 50%, transparent 70%)`,\n                }}\n                animate={{ x: ['-100%', '100%'] }}\n                transition={{ duration: 1.5, ease: \"easeInOut\" }}\n              />\n            )}\n          </div>\n        </motion.div>\n\n        {/* Stats Display */}\n        <div className=\"absolute top-4 right-4 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-3\">\n          <div className=\"text-xs text-text-secondary mb-1\">Card Stats</div>\n          <div className=\"text-sm text-text-primary\">\n            Scans: <span style={{ color: cardColor }}>{data.scanCount}</span>\n          </div>\n          <div className=\"text-sm text-text-primary\">\n            Shares: <span style={{ color: cardColor }}>{data.shareCount}</span>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Simple Camera Simulator\n  const CameraSimulator = ({ data, onScan }) => {\n    const [isScanning, setIsScanning] = useState(false);\n    const [showCard, setShowCard] = useState(false);\n\n    const startScan = () => {\n      setIsScanning(true);\n      setTimeout(() => {\n        setIsScanning(false);\n        setShowCard(true);\n        onScan?.();\n      }, 2000);\n    };\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"text-center\">\n          <Button onClick={startScan} disabled={isScanning}>\n            {isScanning ? '🔍 Scanning...' : '📷 Start AR Scan'}\n          </Button>\n        </div>\n\n        <div className=\"relative w-full max-w-lg mx-auto aspect-video bg-black rounded-xl overflow-hidden border-2 border-border\">\n          {/* Simulated Camera Feed */}\n          <div className=\"relative w-full h-full bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900\">\n            {isScanning && (\n              <motion.div\n                className=\"absolute left-0 right-0 h-1 bg-primary-cyan\"\n                animate={{ top: ['0%', '100%'] }}\n                transition={{ duration: 2, ease: \"linear\" }}\n              />\n            )}\n\n            {showCard && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.5 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n              >\n                <div className=\"w-48 h-32 bg-surface border-2 border-primary-cyan rounded-lg p-3\">\n                  <div className=\"text-primary-cyan text-sm font-bold\">{data.userName}</div>\n                  <div className=\"text-text-secondary text-xs\">{data.userTitle}</div>\n                  <div className=\"text-text-muted text-xs\">{data.userCompany}</div>\n                </div>\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderCurrentComponent = () => {\n    switch (currentLevel) {\n      case 1:\n        return <ARCard data={demoData} onInteraction={() => completeLevel(1)} />;\n      case 2:\n        return (\n          <CameraSimulator \n            data={demoData} \n            onScan={() => {\n              setDemoData(prev => ({ ...prev, scanCount: prev.scanCount + 1 }));\n              completeLevel(2);\n            }}\n          />\n        );\n      case 3:\n        return (\n          <div className=\"space-y-6\">\n            <ARCard data={demoData} />\n            <div className=\"grid grid-cols-4 gap-2 max-w-xs mx-auto\">\n              {['cyan', 'purple', 'green', 'orange'].map(color => (\n                <button\n                  key={color}\n                  onClick={() => {\n                    setDemoData(prev => ({ ...prev, cardColor: color }));\n                    completeLevel(3);\n                  }}\n                  className={`w-12 h-12 rounded-lg border-2 transition-all ${\n                    demoData.cardColor === color ? 'border-white scale-110' : 'border-transparent'\n                  }`}\n                  style={{\n                    background: color === 'cyan' ? '#00f5ff' : \n                              color === 'purple' ? '#8b5cf6' :\n                              color === 'green' ? '#10b981' : '#f59e0b'\n                  }}\n                />\n              ))}\n            </div>\n          </div>\n        );\n      case 4:\n        return (\n          <div className=\"text-center space-y-6\">\n            <div className=\"bg-white p-4 rounded-lg inline-block\">\n              <div className=\"w-32 h-32 grid grid-cols-8 gap-1\">\n                {[...Array(64)].map((_, i) => (\n                  <div key={i} className={`aspect-square ${Math.random() > 0.5 ? 'bg-black' : 'bg-white'}`} />\n                ))}\n              </div>\n            </div>\n            <Button onClick={() => {\n              setDemoData(prev => ({ ...prev, shareCount: prev.shareCount + 1 }));\n              completeLevel(4);\n            }}>\n              📤 Share Card\n            </Button>\n          </div>\n        );\n      case 5:\n        return (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              <div className=\"bg-surface border border-border rounded-lg p-4 text-center\">\n                <div className=\"text-2xl font-bold text-primary-cyan\">{1247 + demoData.scanCount}</div>\n                <div className=\"text-sm text-text-secondary\">Total Scans</div>\n              </div>\n              <div className=\"bg-surface border border-border rounded-lg p-4 text-center\">\n                <div className=\"text-2xl font-bold text-accent-green\">{892 + demoData.shareCount}</div>\n                <div className=\"text-sm text-text-secondary\">Unique Views</div>\n              </div>\n            </div>\n            <Button onClick={() => completeLevel(5)}>\n              📊 View Full Analytics\n            </Button>\n          </div>\n        );\n      default:\n        return <div>Loading...</div>;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background py-12\">\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-hero gradient-text mb-6\">\n            Interactive AR Demo\n          </h1>\n          <p className=\"text-xl text-text-secondary mb-8 max-w-3xl mx-auto\">\n            Experience the complete NameCardAI journey. Go through each level to see how AR business cards revolutionize networking.\n          </p>\n          \n          {/* Progress */}\n          <div className=\"max-w-md mx-auto mb-8\">\n            <div className=\"flex justify-between text-sm text-text-secondary mb-2\">\n              <span>Progress</span>\n              <span>{completedLevels.size}/{levels.length} Complete</span>\n            </div>\n            <div className=\"w-full bg-border rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${(completedLevels.size / levels.length) * 100}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-4 gap-8\">\n          {/* Level Navigation */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 sticky top-24\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-4\">Demo Levels</h3>\n              <div className=\"space-y-3\">\n                {levels.map((level) => (\n                  <motion.div\n                    key={level.id}\n                    whileHover={{ scale: 1.02 }}\n                    className={`p-3 rounded-lg border cursor-pointer transition-all ${\n                      currentLevel === level.id\n                        ? 'bg-primary-cyan/10 border-primary-cyan shadow-lg shadow-primary-cyan/25'\n                        : completedLevels.has(level.id)\n                        ? 'bg-accent-green/10 border-accent-green'\n                        : 'bg-surface border-border hover:border-primary-cyan/50'\n                    }`}\n                    onClick={() => setCurrentLevel(level.id)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                        completedLevels.has(level.id)\n                          ? 'bg-accent-green text-black'\n                          : currentLevel === level.id\n                          ? 'bg-primary-cyan text-black'\n                          : 'bg-border text-text-secondary'\n                      }`}>\n                        {completedLevels.has(level.id) ? '✓' : level.id}\n                      </div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-semibold text-text-primary text-sm\">{level.title}</h4>\n                        <p className=\"text-xs text-text-secondary\">{level.description}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n              \n              <div className=\"mt-6 pt-6 border-t border-border\">\n                <Button variant=\"outline\" size=\"sm\" onClick={resetDemo} className=\"w-full\">\n                  🔄 Reset Demo\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Demo Area */}\n          <div className=\"lg:col-span-3\">\n            <motion.div\n              key={currentLevel}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-8 min-h-[600px] relative\"\n            >\n              {/* Level Header */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-primary-cyan text-black rounded-full flex items-center justify-center font-bold\">\n                    {currentLevel}\n                  </div>\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-text-primary\">\n                      {currentLevelData?.title}\n                    </h2>\n                    <p className=\"text-text-secondary\">\n                      {currentLevelData?.description}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Demo Component */}\n              <div className=\"relative\">\n                {renderCurrentComponent()}\n              </div>\n\n              {/* Instructions Overlay */}\n              <AnimatePresence>\n                {showInstructions && currentLevelData && (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\"\n                  >\n                    <motion.div\n                      initial={{ scale: 0.8, opacity: 0 }}\n                      animate={{ scale: 1, opacity: 1 }}\n                      exit={{ scale: 0.8, opacity: 0 }}\n                      className=\"bg-surface border border-border rounded-xl p-8 max-w-md mx-4 text-center\"\n                    >\n                      <div className=\"text-4xl mb-4\">💡</div>\n                      <h3 className=\"text-xl font-bold text-text-primary mb-4\">Instructions</h3>\n                      <p className=\"text-text-secondary mb-6\">{currentLevelData.instruction}</p>\n                      <div className=\"flex space-x-3 justify-center\">\n                        <Button onClick={() => setShowInstructions(false)}>Got it!</Button>\n                        <Button variant=\"ghost\" onClick={() => setShowInstructions(false)}>Skip Tutorial</Button>\n                      </div>\n                    </motion.div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Completion Message */}\n        <AnimatePresence>\n          {completedLevels.size === levels.length && (\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -50 }}\n              className=\"mt-12 text-center bg-gradient-to-r from-accent-green/10 to-primary-cyan/10 border border-accent-green/30 rounded-xl p-8\"\n            >\n              <div className=\"text-6xl mb-4\">🎉</div>\n              <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n                Demo Complete!\n              </h3>\n              <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n                Congratulations! You've experienced the full NameCardAI journey. Ready to create your own AR business card?\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" href=\"/signup\">\n                  ✨ Start Your AR Journey\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" onClick={resetDemo}>\n                  🔄 Try Demo Again\n                </Button>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,WAAW;QACX,aAAa;QACb,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,YAAY;IACd;IAEA,MAAM,SAAS;QACb;YACE,IAAI;YAC<PERSON>,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE3D,MAAM,gBAAgB,CAAC;QACrB,mBAAmB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAQ;QACrD,IAAI,YAAY,gBAAgB,UAAU,OAAO,MAAM,EAAE;YACvD,WAAW,IAAM,gBAAgB,UAAU,IAAI;QACjD;IACF;IAEA,MAAM,YAAY;QAChB,gBAAgB;QAChB,mBAAmB,IAAI;QACvB,oBAAoB;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;gBACX,YAAY;YACd,CAAC;IACH;IAEA,2BAA2B;IAC3B,MAAM,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE;;QACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;YAAE,GAAG;YAAG,GAAG;QAAE;QAChE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAEvB,MAAM,kBAAkB,CAAC;YACvB,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;gBAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;gBAEzC,iBAAiB;oBACf,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;oBAC3B,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;gBAC7B;YACF;QACF;QAEA,MAAM,QAAQ;YACZ,MAAM;YACN,QAAQ;YACR,OAAO;YACP,QAAQ;QACV;QAEA,MAAM,YAAY,KAAK,CAAC,KAAK,SAAS,CAAC,IAAI,MAAM,IAAI;QAErD,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC;wBAC9F,gBAAgB;oBAClB;oBACA,aAAa;oBACb,cAAc,IAAM,aAAa;oBACjC,cAAc;wBACZ,aAAa;wBACb,iBAAiB;4BAAE,GAAG;4BAAG,GAAG;wBAAE;oBAChC;oBACA,SAAS,IAAM;oBACf,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,SAAS;wBACP,WAAW;4BAAC,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;4BAAE,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;4BAAE,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;yBAAC;oBAC9F;oBACA,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAE5C,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,8DAA8D,CAAC;4BAC5E,aAAa;4BACb,WAAW,YAAY,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,GAAG;wBACrD;;0CAGA,6LAAC;gCAAI,WAAU;gCAAoE,OAAO;oCAAE,aAAa;gCAAU;;;;;;0CACnH,6LAAC;gCAAI,WAAU;gCAAqE,OAAO;oCAAE,aAAa;gCAAU;;;;;;0CACpH,6LAAC;gCAAI,WAAU;gCAAuE,OAAO;oCAAE,aAAa;gCAAU;;;;;;0CACtH,6LAAC;gCAAI,WAAU;gCAAwE,OAAO;oCAAE,aAAa;gCAAU;;;;;;0CAGvH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,OAAO;4DAAE,YAAY;wDAAU;wDAC/B,SAAS;4DAAE,QAAQ,YAAY,MAAM;wDAAE;wDACvC,YAAY;4DAAE,UAAU;wDAAE;kEAEzB,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAGhD,6LAAC;wDAAG,WAAU;kEAAqC,KAAK,QAAQ;;;;;;kEAChE,6LAAC;wDAAE,WAAU;kEAA8B,KAAK,SAAS;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;0DAGxD,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,YAAY,YAAY;oDAAM,OAAO;gDAAU;0DACzD;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAW,KAAK,SAAS;;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,OAAO;4DAAE,OAAO;wDAAU;kEAAG;;;;;;kEACnC,6LAAC;wDAAK,WAAU;kEAAW,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;4BAM9C,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCACL,YAAY,CAAC,wCAAwC,EAAE,UAAU,wBAAwB,CAAC;gCAC5F;gCACA,SAAS;oCAAE,GAAG;wCAAC;wCAAS;qCAAO;gCAAC;gCAChC,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;;;;;;;;;;;;;;;;;8BAOvD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAmC;;;;;;sCAClD,6LAAC;4BAAI,WAAU;;gCAA4B;8CAClC,6LAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAU;8CAAI,KAAK,SAAS;;;;;;;;;;;;sCAE3D,6LAAC;4BAAI,WAAU;;gCAA4B;8CACjC,6LAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAU;8CAAI,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;IAKrE;QA/HM;IAiIN,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;;QACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAEzC,MAAM,YAAY;YAChB,cAAc;YACd,WAAW;gBACT,cAAc;gBACd,YAAY;gBACZ;YACF,GAAG;QACL;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;wBAAC,SAAS;wBAAW,UAAU;kCACnC,aAAa,mBAAmB;;;;;;;;;;;8BAIrC,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,4BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,KAAK;wCAAC;wCAAM;qCAAO;gCAAC;gCAC/B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAS;;;;;;4BAI7C,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAuC,KAAK,QAAQ;;;;;;sDACnE,6LAAC;4CAAI,WAAU;sDAA+B,KAAK,SAAS;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDAA2B,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1E;QAjDM;IAmDN,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC;oBAAO,MAAM;oBAAU,eAAe,IAAM,cAAc;;;;;;YACpE,KAAK;gBACH,qBACE,6LAAC;oBACC,MAAM;oBACN,QAAQ;wBACN,YAAY,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,WAAW,KAAK,SAAS,GAAG;4BAAE,CAAC;wBAC/D,cAAc;oBAChB;;;;;;YAGN,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,MAAM;;;;;;sCACd,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAQ;gCAAU;gCAAS;6BAAS,CAAC,GAAG,CAAC,CAAA,sBACzC,6LAAC;oCAEC,SAAS;wCACP,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,WAAW;4CAAM,CAAC;wCAClD,cAAc;oCAChB;oCACA,WAAW,CAAC,6CAA6C,EACvD,SAAS,SAAS,KAAK,QAAQ,2BAA2B,sBAC1D;oCACF,OAAO;wCACL,YAAY,UAAU,SAAS,YACrB,UAAU,WAAW,YACrB,UAAU,UAAU,YAAY;oCAC5C;mCAZK;;;;;;;;;;;;;;;;YAkBjB,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC;wCAAY,WAAW,CAAC,cAAc,EAAE,KAAK,MAAM,KAAK,MAAM,aAAa,YAAY;uCAA9E;;;;;;;;;;;;;;;sCAIhB,6LAAC,oIAAA,CAAA,UAAM;4BAAC,SAAS;gCACf,YAAY,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,YAAY,KAAK,UAAU,GAAG;oCAAE,CAAC;gCACjE,cAAc;4BAChB;sCAAG;;;;;;;;;;;;YAKT,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAwC,OAAO,SAAS,SAAS;;;;;;sDAChF,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAE/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAwC,MAAM,SAAS,UAAU;;;;;;sDAChF,6LAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAGjD,6LAAC,oIAAA,CAAA,UAAM;4BAAC,SAAS,IAAM,cAAc;sCAAI;;;;;;;;;;;;YAK/C;gBACE,qBAAO,6LAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAG7C,6LAAC;4BAAE,WAAU;sCAAqD;;;;;;sCAKlE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;;gDAAM,gBAAgB,IAAI;gDAAC;gDAAE,OAAO,MAAM;gDAAC;;;;;;;;;;;;;8CAE9C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO,GAAG,AAAC,gBAAgB,IAAI,GAAG,OAAO,MAAM,GAAI,IAAI,CAAC,CAAC;wCAAC;wCACrE,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,WAAW,CAAC,oDAAoD,EAC9D,iBAAiB,MAAM,EAAE,GACrB,4EACA,gBAAgB,GAAG,CAAC,MAAM,EAAE,IAC5B,2CACA,yDACJ;gDACF,SAAS,IAAM,gBAAgB,MAAM,EAAE;0DAEvC,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,CAAC,wEAAwE,EACvF,gBAAgB,GAAG,CAAC,MAAM,EAAE,IACxB,+BACA,iBAAiB,MAAM,EAAE,GACzB,+BACA,iCACJ;sEACC,gBAAgB,GAAG,CAAC,MAAM,EAAE,IAAI,MAAM,MAAM,EAAE;;;;;;sEAEjD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA2C,MAAM,KAAK;;;;;;8EACpE,6LAAC;oEAAE,WAAU;8EAA+B,MAAM,WAAW;;;;;;;;;;;;;;;;;;+CAvB5D,MAAM,EAAE;;;;;;;;;;kDA8BnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;4CAAW,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAQjF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEACX,kBAAkB;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEACV,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;kDAO3B,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIH,6LAAC,4LAAA,CAAA,kBAAe;kDACb,oBAAoB,kCACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,MAAM;gDAAE,SAAS;4CAAE;4CACnB,WAAU;sDAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;oDAAK,SAAS;gDAAE;gDAClC,SAAS;oDAAE,OAAO;oDAAG,SAAS;gDAAE;gDAChC,MAAM;oDAAE,OAAO;oDAAK,SAAS;gDAAE;gDAC/B,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,6LAAC;wDAAE,WAAU;kEAA4B,iBAAiB,WAAW;;;;;;kEACrE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,UAAM;gEAAC,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;0EACnD,6LAAC,oIAAA,CAAA,UAAM;gEAAC,SAAQ;gEAAQ,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAhDxE;;;;;;;;;;;;;;;;8BA2DX,6LAAC,4LAAA,CAAA,kBAAe;8BACb,gBAAgB,IAAI,KAAK,OAAO,MAAM,kBACrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,MAAK;kDAAU;;;;;;kDAGjC,6LAAC,oIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE;GApfwB;KAAA", "debugId": null}}]}