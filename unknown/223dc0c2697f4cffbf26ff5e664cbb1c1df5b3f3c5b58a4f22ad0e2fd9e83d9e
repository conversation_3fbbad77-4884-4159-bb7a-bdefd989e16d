/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Post-Render Event.
 *
 * This event is dispatched by the Renderer when all rendering, for all cameras in all Scenes,
 * has completed, but before any pending snap shots have been taken.
 *
 * @event Phaser.Renderer.Events#POST_RENDER
 * @type {string}
 * @since 3.50.0
 */
module.exports = 'postrender';
