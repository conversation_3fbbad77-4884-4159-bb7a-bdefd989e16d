{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,OAAO,kBACX,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;wBAAmC;sCACvC,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMpD,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;;;;kDACD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAKP,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;AAIU,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,8OAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,8OAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,8OAAC;;sEACC,8OAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,8OAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;4CAAG,WAAU;;gDAAkC;8DACtC,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/ARCardRenderer.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\n\nconst ARCardRenderer = ({ data, onInteraction }) => {\n  const cardRef = useRef(null);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isHovered, setIsHovered] = useState(false);\n  const [hasInteracted, setHasInteracted] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (cardRef.current) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        \n        setMousePosition({\n          x: (e.clientX - centerX) / 15,\n          y: (e.clientY - centerY) / 15\n        });\n      }\n    };\n\n    const handleMouseEnter = () => {\n      setIsHovered(true);\n      if (!hasInteracted) {\n        setHasInteracted(true);\n        onInteraction?.();\n      }\n    };\n\n    const handleMouseLeave = () => {\n      setIsHovered(false);\n      setMousePosition({ x: 0, y: 0 });\n    };\n\n    const card = cardRef.current;\n    if (card) {\n      card.addEventListener('mousemove', handleMouseMove);\n      card.addEventListener('mouseenter', handleMouseEnter);\n      card.addEventListener('mouseleave', handleMouseLeave);\n    }\n\n    return () => {\n      if (card) {\n        card.removeEventListener('mousemove', handleMouseMove);\n        card.removeEventListener('mouseenter', handleMouseEnter);\n        card.removeEventListener('mouseleave', handleMouseLeave);\n      }\n    };\n  }, [hasInteracted, onInteraction]);\n\n  const getColorTheme = (color) => {\n    const themes = {\n      cyan: {\n        primary: '#00f5ff',\n        secondary: '#0891b2',\n        gradient: 'linear-gradient(135deg, #00f5ff, #0891b2)',\n        glow: '0 0 20px rgba(0, 245, 255, 0.4)'\n      },\n      purple: {\n        primary: '#8b5cf6',\n        secondary: '#7c3aed',\n        gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',\n        glow: '0 0 20px rgba(139, 92, 246, 0.4)'\n      },\n      green: {\n        primary: '#10b981',\n        secondary: '#059669',\n        gradient: 'linear-gradient(135deg, #10b981, #059669)',\n        glow: '0 0 20px rgba(16, 185, 129, 0.4)'\n      },\n      orange: {\n        primary: '#f59e0b',\n        secondary: '#d97706',\n        gradient: 'linear-gradient(135deg, #f59e0b, #d97706)',\n        glow: '0 0 20px rgba(245, 158, 11, 0.4)'\n      }\n    };\n    return themes[color] || themes.cyan;\n  };\n\n  const theme = getColorTheme(data.cardColor);\n\n  const getEffectAnimation = (effect) => {\n    switch (effect) {\n      case 'glow':\n        return {\n          boxShadow: [theme.glow, `0 0 30px ${theme.primary}40`, theme.glow],\n          transition: { duration: 2, repeat: Infinity }\n        };\n      case 'float':\n        return {\n          y: [0, -10, 0],\n          transition: { duration: 3, repeat: Infinity, ease: \"easeInOut\" }\n        };\n      case 'pulse':\n        return {\n          scale: [1, 1.05, 1],\n          transition: { duration: 2, repeat: Infinity }\n        };\n      case 'spin':\n        return {\n          rotateY: [0, 360],\n          transition: { duration: 8, repeat: Infinity, ease: \"linear\" }\n        };\n      default:\n        return {};\n    }\n  };\n\n  return (\n    <div className=\"flex items-center justify-center min-h-[400px] relative\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        {/* Floating Particles */}\n        {[...Array(12)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 rounded-full\"\n            style={{\n              background: theme.primary,\n              left: `${20 + i * 7}%`,\n              top: `${30 + (i % 3) * 20}%`,\n            }}\n            animate={{\n              y: [-20, 20, -20],\n              opacity: [0.3, 1, 0.3],\n              scale: [0.5, 1, 0.5],\n            }}\n            transition={{\n              duration: 3 + i * 0.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: i * 0.2\n            }}\n          />\n        ))}\n\n        {/* AR Grid */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"grid grid-cols-8 grid-rows-6 h-full w-full\">\n            {[...Array(48)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"border border-primary-cyan/20\"\n                animate={{\n                  opacity: [0, 0.5, 0],\n                }}\n                transition={{\n                  duration: 4,\n                  delay: i * 0.05,\n                  repeat: Infinity,\n                  repeatDelay: 2\n                }}\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Main Card */}\n      <motion.div\n        ref={cardRef}\n        className=\"relative w-96 h-60 cursor-pointer\"\n        style={{\n          transform: `perspective(1000px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg)`,\n          transformStyle: 'preserve-3d'\n        }}\n        animate={getEffectAnimation(data.arEffect)}\n        whileHover={{ scale: 1.05 }}\n      >\n        {/* Card Base */}\n        <div \n          className=\"absolute inset-0 rounded-xl shadow-2xl border-2 overflow-hidden\"\n          style={{\n            background: `linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%)`,\n            borderColor: theme.primary,\n            boxShadow: isHovered ? theme.glow : '0 10px 30px rgba(0,0,0,0.3)'\n          }}\n        >\n          {/* AR Corner Indicators */}\n          <div className=\"absolute top-3 left-3 w-6 h-6 border-l-2 border-t-2 animate-pulse\" style={{ borderColor: theme.primary }} />\n          <div className=\"absolute top-3 right-3 w-6 h-6 border-r-2 border-t-2 animate-pulse\" style={{ borderColor: theme.primary }} />\n          <div className=\"absolute bottom-3 left-3 w-6 h-6 border-l-2 border-b-2 animate-pulse\" style={{ borderColor: theme.primary }} />\n          <div className=\"absolute bottom-3 right-3 w-6 h-6 border-r-2 border-b-2 animate-pulse\" style={{ borderColor: theme.primary }} />\n\n          {/* Card Content */}\n          <div className=\"p-8 h-full flex flex-col justify-between relative z-10\">\n            {/* Header */}\n            <div className=\"flex items-start justify-between\">\n              <div>\n                {/* Avatar */}\n                <motion.div\n                  className=\"w-16 h-16 rounded-full mb-4 flex items-center justify-center text-2xl font-bold text-black\"\n                  style={{ background: theme.gradient }}\n                  animate={{ rotate: isHovered ? 360 : 0 }}\n                  transition={{ duration: 1 }}\n                >\n                  {data.userName.split(' ').map(n => n[0]).join('')}\n                </motion.div>\n                \n                {/* Name & Title */}\n                <h3 className=\"text-xl font-bold text-white mb-1\">{data.userName}</h3>\n                <p className=\"text-gray-300 text-sm mb-1\">{data.userTitle}</p>\n                <p className=\"text-gray-400 text-xs\">{data.userCompany}</p>\n              </div>\n\n              {/* Company Logo Placeholder */}\n              <div \n                className=\"w-12 h-12 rounded-lg flex items-center justify-center text-xs font-bold\"\n                style={{ background: theme.primary + '20', color: theme.primary }}\n              >\n                LOGO\n              </div>\n            </div>\n\n            {/* Contact Info */}\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <span style={{ color: theme.primary }}>📧</span>\n                <span>{data.userEmail}</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm text-gray-300\">\n                <span style={{ color: theme.primary }}>📱</span>\n                <span>{data.userPhone}</span>\n              </div>\n            </div>\n\n            {/* Social Links */}\n            <div className=\"flex space-x-3\">\n              {['LinkedIn', 'Twitter', 'GitHub'].map((platform, i) => (\n                <motion.div\n                  key={platform}\n                  className=\"w-8 h-8 rounded border flex items-center justify-center text-xs cursor-pointer\"\n                  style={{ \n                    borderColor: theme.primary + '40',\n                    background: theme.primary + '10',\n                    color: theme.primary\n                  }}\n                  whileHover={{ \n                    scale: 1.1,\n                    background: theme.primary + '20'\n                  }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  {platform[0]}\n                </motion.div>\n              ))}\n            </div>\n          </div>\n\n          {/* Holographic Overlay */}\n          <motion.div\n            className=\"absolute inset-0 rounded-xl pointer-events-none\"\n            style={{\n              background: `linear-gradient(45deg, transparent 30%, ${theme.primary}10 50%, transparent 70%)`,\n            }}\n            animate={{\n              x: isHovered ? ['-100%', '100%'] : 0,\n            }}\n            transition={{\n              duration: 1.5,\n              ease: \"easeInOut\"\n            }}\n          />\n\n          {/* Scan Line Effect */}\n          {isHovered && (\n            <motion.div\n              className=\"absolute left-0 right-0 h-0.5 pointer-events-none\"\n              style={{ background: theme.primary }}\n              animate={{\n                top: ['0%', '100%'],\n                opacity: [0, 1, 0]\n              }}\n              transition={{\n                duration: 2,\n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n            />\n          )}\n        </div>\n\n        {/* 3D Shadow */}\n        <div \n          className=\"absolute inset-0 rounded-xl -z-10\"\n          style={{\n            transform: 'translateZ(-20px)',\n            background: 'rgba(0,0,0,0.3)',\n            filter: 'blur(10px)'\n          }}\n        />\n      </motion.div>\n\n      {/* Interaction Hint */}\n      {!hasInteracted && (\n        <motion.div\n          className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center\"\n          animate={{ y: [0, -10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"bg-surface/90 backdrop-blur-sm border border-border rounded-lg px-4 py-2\">\n            <p className=\"text-sm text-text-secondary\">\n              🖱️ Hover over the card to see the 3D effect\n            </p>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Stats Display */}\n      <div className=\"absolute top-4 right-4 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-3\">\n        <div className=\"text-xs text-text-secondary mb-1\">Card Stats</div>\n        <div className=\"text-sm text-text-primary\">\n          Scans: <span style={{ color: theme.primary }}>{data.scanCount}</span>\n        </div>\n        <div className=\"text-sm text-text-primary\">\n          Shares: <span style={{ color: theme.primary }}>{data.shareCount}</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ARCardRenderer;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE;IAC7C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;gBAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;gBAEzC,iBAAiB;oBACf,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;oBAC3B,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;gBAC7B;YACF;QACF;QAEA,MAAM,mBAAmB;YACvB,aAAa;YACb,IAAI,CAAC,eAAe;gBAClB,iBAAiB;gBACjB;YACF;QACF;QAEA,MAAM,mBAAmB;YACvB,aAAa;YACb,iBAAiB;gBAAE,GAAG;gBAAG,GAAG;YAAE;QAChC;QAEA,MAAM,OAAO,QAAQ,OAAO;QAC5B,IAAI,MAAM;YACR,KAAK,gBAAgB,CAAC,aAAa;YACnC,KAAK,gBAAgB,CAAC,cAAc;YACpC,KAAK,gBAAgB,CAAC,cAAc;QACtC;QAEA,OAAO;YACL,IAAI,MAAM;gBACR,KAAK,mBAAmB,CAAC,aAAa;gBACtC,KAAK,mBAAmB,CAAC,cAAc;gBACvC,KAAK,mBAAmB,CAAC,cAAc;YACzC;QACF;IACF,GAAG;QAAC;QAAe;KAAc;IAEjC,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS;YACb,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;YACA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QACA,OAAO,MAAM,CAAC,MAAM,IAAI,OAAO,IAAI;IACrC;IAEA,MAAM,QAAQ,cAAc,KAAK,SAAS;IAE1C,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;wBAAC,MAAM,IAAI;wBAAE,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;wBAAE,MAAM,IAAI;qBAAC;oBAClE,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;gBAC9C;YACF,KAAK;gBACH,OAAO;oBACL,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;oBACd,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAY;gBACjE;YACF,KAAK;gBACH,OAAO;oBACL,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;oBACnB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;gBAC9C;YACF,KAAK;gBACH,OAAO;oBACL,SAAS;wBAAC;wBAAG;qBAAI;oBACjB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;gBAC9D;YACF;gBACE,OAAO,CAAC;QACZ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;oBAEZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,YAAY,MAAM,OAAO;gCACzB,MAAM,GAAG,KAAK,IAAI,EAAE,CAAC,CAAC;gCACtB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;4BAC9B;4BACA,SAAS;gCACP,GAAG;oCAAC,CAAC;oCAAI;oCAAI,CAAC;iCAAG;gCACjB,SAAS;oCAAC;oCAAK;oCAAG;iCAAI;gCACtB,OAAO;oCAAC;oCAAK;oCAAG;iCAAI;4BACtB;4BACA,YAAY;gCACV,UAAU,IAAI,IAAI;gCAClB,QAAQ;gCACR,MAAM;gCACN,OAAO,IAAI;4BACb;2BAjBK;;;;;kCAsBT,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAK;yCAAE;oCACtB;oCACA,YAAY;wCACV,UAAU;wCACV,OAAO,IAAI;wCACX,QAAQ;wCACR,aAAa;oCACf;mCAVK;;;;;;;;;;;;;;;;;;;;;0BAkBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,WAAU;gBACV,OAAO;oBACL,WAAW,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC;oBAC9F,gBAAgB;gBAClB;gBACA,SAAS,mBAAmB,KAAK,QAAQ;gBACzC,YAAY;oBAAE,OAAO;gBAAK;;kCAG1B,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,8DAA8D,CAAC;4BAC5E,aAAa,MAAM,OAAO;4BAC1B,WAAW,YAAY,MAAM,IAAI,GAAG;wBACtC;;0CAGA,8OAAC;gCAAI,WAAU;gCAAoE,OAAO;oCAAE,aAAa,MAAM,OAAO;gCAAC;;;;;;0CACvH,8OAAC;gCAAI,WAAU;gCAAqE,OAAO;oCAAE,aAAa,MAAM,OAAO;gCAAC;;;;;;0CACxH,8OAAC;gCAAI,WAAU;gCAAuE,OAAO;oCAAE,aAAa,MAAM,OAAO;gCAAC;;;;;;0CAC1H,8OAAC;gCAAI,WAAU;gCAAwE,OAAO;oCAAE,aAAa,MAAM,OAAO;gCAAC;;;;;;0CAG3H,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,OAAO;4DAAE,YAAY,MAAM,QAAQ;wDAAC;wDACpC,SAAS;4DAAE,QAAQ,YAAY,MAAM;wDAAE;wDACvC,YAAY;4DAAE,UAAU;wDAAE;kEAEzB,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;kEAIhD,8OAAC;wDAAG,WAAU;kEAAqC,KAAK,QAAQ;;;;;;kEAChE,8OAAC;wDAAE,WAAU;kEAA8B,KAAK,SAAS;;;;;;kEACzD,8OAAC;wDAAE,WAAU;kEAAyB,KAAK,WAAW;;;;;;;;;;;;0DAIxD,8OAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,YAAY,MAAM,OAAO,GAAG;oDAAM,OAAO,MAAM,OAAO;gDAAC;0DACjE;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,OAAO,MAAM,OAAO;wDAAC;kEAAG;;;;;;kEACvC,8OAAC;kEAAM,KAAK,SAAS;;;;;;;;;;;;0DAEvB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,OAAO;4DAAE,OAAO,MAAM,OAAO;wDAAC;kEAAG;;;;;;kEACvC,8OAAC;kEAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;kDAKzB,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAY;4CAAW;yCAAS,CAAC,GAAG,CAAC,CAAC,UAAU,kBAChD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDACL,aAAa,MAAM,OAAO,GAAG;oDAC7B,YAAY,MAAM,OAAO,GAAG;oDAC5B,OAAO,MAAM,OAAO;gDACtB;gDACA,YAAY;oDACV,OAAO;oDACP,YAAY,MAAM,OAAO,GAAG;gDAC9B;gDACA,UAAU;oDAAE,OAAO;gDAAI;0DAEtB,QAAQ,CAAC,EAAE;+CAbP;;;;;;;;;;;;;;;;0CAoBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCACL,YAAY,CAAC,wCAAwC,EAAE,MAAM,OAAO,CAAC,wBAAwB,CAAC;gCAChG;gCACA,SAAS;oCACP,GAAG,YAAY;wCAAC;wCAAS;qCAAO,GAAG;gCACrC;gCACA,YAAY;oCACV,UAAU;oCACV,MAAM;gCACR;;;;;;4BAID,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCAAE,YAAY,MAAM,OAAO;gCAAC;gCACnC,SAAS;oCACP,KAAK;wCAAC;wCAAM;qCAAO;oCACnB,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;;;;;;;;;;;;kCAMN,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,WAAW;4BACX,YAAY;4BACZ,QAAQ;wBACV;;;;;;;;;;;;YAKH,CAAC,+BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG,CAAC;wBAAI;qBAAE;gBAAC;gBAC1B,YAAY;oBAAE,UAAU;oBAAG,QAAQ;gBAAS;0BAE5C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;0BAQjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAmC;;;;;;kCAClD,8OAAC;wBAAI,WAAU;;4BAA4B;0CAClC,8OAAC;gCAAK,OAAO;oCAAE,OAAO,MAAM,OAAO;gCAAC;0CAAI,KAAK,SAAS;;;;;;;;;;;;kCAE/D,8OAAC;wBAAI,WAAU;;4BAA4B;0CACjC,8OAAC;gCAAK,OAAO;oCAAE,OAAO,MAAM,OAAO;gCAAC;0CAAI,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKzE;uCAEe", "debugId": null}}, {"offset": {"line": 1901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/CameraSimulator.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from '../ui/Button';\n\nconst CameraSimulator = ({ data, onScan }) => {\n  const [isScanning, setIsScanning] = useState(false);\n  const [scanProgress, setScanProgress] = useState(0);\n  const [showCard, setShowCard] = useState(false);\n  const [cameraActive, setCameraActive] = useState(false);\n  const videoRef = useRef(null);\n\n  const startScan = () => {\n    setIsScanning(true);\n    setScanProgress(0);\n    \n    // Simulate scanning progress\n    const interval = setInterval(() => {\n      setScanProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(interval);\n          setIsScanning(false);\n          setShowCard(true);\n          onScan?.();\n          return 100;\n        }\n        return prev + 2;\n      });\n    }, 50);\n  };\n\n  const resetScan = () => {\n    setShowCard(false);\n    setScanProgress(0);\n    setIsScanning(false);\n  };\n\n  const activateCamera = () => {\n    setCameraActive(true);\n  };\n\n  const theme = {\n    cyan: '#00f5ff',\n    purple: '#8b5cf6',\n    green: '#10b981',\n    orange: '#f59e0b'\n  };\n\n  const cardColor = theme[data.cardColor] || theme.cyan;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Camera Controls */}\n      <div className=\"flex justify-center space-x-4 mb-6\">\n        <Button \n          onClick={activateCamera}\n          variant={cameraActive ? \"primary\" : \"outline\"}\n          disabled={cameraActive}\n        >\n          📷 {cameraActive ? 'Camera Active' : 'Activate Camera'}\n        </Button>\n        <Button \n          onClick={startScan}\n          disabled={!cameraActive || isScanning}\n          variant=\"outline\"\n        >\n          🔍 {isScanning ? 'Scanning...' : 'Start AR Scan'}\n        </Button>\n        <Button \n          onClick={resetScan}\n          variant=\"ghost\"\n        >\n          🔄 Reset\n        </Button>\n      </div>\n\n      {/* Camera View */}\n      <div className=\"relative w-full max-w-2xl mx-auto\">\n        <div className=\"aspect-video bg-black rounded-xl overflow-hidden border-2 border-border relative\">\n          {/* Simulated Camera Feed */}\n          {cameraActive ? (\n            <div className=\"relative w-full h-full\">\n              {/* Background Pattern (simulating real world) */}\n              <div className=\"absolute inset-0 bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900\">\n                {/* Simulated Environment */}\n                <div className=\"absolute inset-0 opacity-30\">\n                  <div className=\"grid grid-cols-8 grid-rows-6 h-full w-full\">\n                    {[...Array(48)].map((_, i) => (\n                      <div\n                        key={i}\n                        className=\"border border-gray-600/20\"\n                        style={{\n                          background: Math.random() > 0.7 ? 'rgba(255,255,255,0.05)' : 'transparent'\n                        }}\n                      />\n                    ))}\n                  </div>\n                </div>\n\n                {/* Simulated Person/Hand */}\n                <div className=\"absolute bottom-20 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"w-32 h-40 bg-gradient-to-t from-amber-900/60 to-amber-700/40 rounded-t-full opacity-60\" />\n                </div>\n              </div>\n\n              {/* AR Scanning Overlay */}\n              {isScanning && (\n                <>\n                  {/* Scanning Grid */}\n                  <div className=\"absolute inset-0\">\n                    <div className=\"grid grid-cols-12 grid-rows-8 h-full w-full\">\n                      {[...Array(96)].map((_, i) => (\n                        <motion.div\n                          key={i}\n                          className=\"border border-primary-cyan/30\"\n                          animate={{\n                            opacity: [0, 1, 0],\n                          }}\n                          transition={{\n                            duration: 0.5,\n                            delay: i * 0.01,\n                            repeat: Infinity,\n                            repeatDelay: 1\n                          }}\n                        />\n                      ))}\n                    </div>\n                  </div>\n\n                  {/* Scanning Line */}\n                  <motion.div\n                    className=\"absolute left-0 right-0 h-1 bg-primary-cyan shadow-lg\"\n                    style={{ boxShadow: '0 0 20px #00f5ff' }}\n                    animate={{\n                      top: ['0%', '100%'],\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    }}\n                  />\n\n                  {/* Corner Brackets */}\n                  <div className=\"absolute inset-8\">\n                    <div className=\"absolute top-0 left-0 w-8 h-8 border-l-2 border-t-2 border-primary-cyan animate-pulse\" />\n                    <div className=\"absolute top-0 right-0 w-8 h-8 border-r-2 border-t-2 border-primary-cyan animate-pulse\" />\n                    <div className=\"absolute bottom-0 left-0 w-8 h-8 border-l-2 border-b-2 border-primary-cyan animate-pulse\" />\n                    <div className=\"absolute bottom-0 right-0 w-8 h-8 border-r-2 border-b-2 border-primary-cyan animate-pulse\" />\n                  </div>\n                </>\n              )}\n\n              {/* AR Business Card Overlay */}\n              <AnimatePresence>\n                {showCard && (\n                  <motion.div\n                    initial={{ opacity: 0, scale: 0.5, y: 50 }}\n                    animate={{ opacity: 1, scale: 1, y: 0 }}\n                    exit={{ opacity: 0, scale: 0.5, y: -50 }}\n                    className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n                  >\n                    {/* AR Card */}\n                    <div \n                      className=\"w-64 h-40 rounded-lg border-2 p-4 backdrop-blur-sm relative\"\n                      style={{\n                        background: 'rgba(26, 26, 26, 0.9)',\n                        borderColor: cardColor,\n                        boxShadow: `0 0 30px ${cardColor}40`\n                      }}\n                    >\n                      {/* AR Indicators */}\n                      <div className=\"absolute -top-2 -left-2 w-4 h-4 border-l-2 border-t-2 animate-pulse\" style={{ borderColor: cardColor }} />\n                      <div className=\"absolute -top-2 -right-2 w-4 h-4 border-r-2 border-t-2 animate-pulse\" style={{ borderColor: cardColor }} />\n                      <div className=\"absolute -bottom-2 -left-2 w-4 h-4 border-l-2 border-b-2 animate-pulse\" style={{ borderColor: cardColor }} />\n                      <div className=\"absolute -bottom-2 -right-2 w-4 h-4 border-r-2 border-b-2 animate-pulse\" style={{ borderColor: cardColor }} />\n\n                      {/* Card Content */}\n                      <div className=\"h-full flex flex-col justify-between\">\n                        <div className=\"flex items-start justify-between\">\n                          <div>\n                            <div \n                              className=\"w-10 h-10 rounded-full mb-2 flex items-center justify-center text-sm font-bold text-black\"\n                              style={{ background: cardColor }}\n                            >\n                              {data.userName.split(' ').map(n => n[0]).join('')}\n                            </div>\n                            <h3 className=\"text-white font-bold text-sm\">{data.userName}</h3>\n                            <p className=\"text-gray-300 text-xs\">{data.userTitle}</p>\n                          </div>\n                          <div \n                            className=\"w-8 h-8 rounded border flex items-center justify-center text-xs\"\n                            style={{ borderColor: cardColor + '40', color: cardColor }}\n                          >\n                            AR\n                          </div>\n                        </div>\n\n                        <div className=\"space-y-1\">\n                          <div className=\"flex items-center space-x-2 text-xs text-gray-300\">\n                            <span style={{ color: cardColor }}>📧</span>\n                            <span>{data.userEmail}</span>\n                          </div>\n                          <div className=\"flex items-center space-x-2 text-xs text-gray-300\">\n                            <span style={{ color: cardColor }}>📱</span>\n                            <span>{data.userPhone}</span>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Floating Particles */}\n                      {[...Array(6)].map((_, i) => (\n                        <motion.div\n                          key={i}\n                          className=\"absolute w-1 h-1 rounded-full\"\n                          style={{\n                            background: cardColor,\n                            left: `${20 + i * 15}%`,\n                            top: `${20 + (i % 2) * 60}%`,\n                          }}\n                          animate={{\n                            y: [-10, 10, -10],\n                            opacity: [0.3, 1, 0.3],\n                          }}\n                          transition={{\n                            duration: 2 + i * 0.3,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                          }}\n                        />\n                      ))}\n                    </div>\n\n                    {/* Action Buttons */}\n                    <motion.div\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: 0.5 }}\n                      className=\"flex justify-center space-x-3 mt-4\"\n                    >\n                      <button \n                        className=\"px-4 py-2 bg-primary-cyan text-black rounded-lg text-sm font-semibold hover:bg-primary-cyan/80 transition-colors\"\n                      >\n                        💾 Save Contact\n                      </button>\n                      <button \n                        className=\"px-4 py-2 bg-surface border border-border text-text-primary rounded-lg text-sm hover:bg-border transition-colors\"\n                      >\n                        📤 Share\n                      </button>\n                    </motion.div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Camera UI Elements */}\n              <div className=\"absolute top-4 left-4 flex items-center space-x-2\">\n                <div className=\"w-3 h-3 bg-red-500 rounded-full animate-pulse\" />\n                <span className=\"text-white text-sm font-medium\">AR Camera</span>\n              </div>\n\n              <div className=\"absolute top-4 right-4 text-white text-sm\">\n                <div className=\"bg-black/50 rounded px-2 py-1\">\n                  {isScanning ? `Scanning... ${scanProgress}%` : showCard ? 'Card Detected!' : 'Ready to Scan'}\n                </div>\n              </div>\n\n              {/* Crosshair */}\n              {cameraActive && !showCard && (\n                <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\">\n                  <div className=\"w-8 h-8 border-2 border-white/50 rounded-full\">\n                    <div className=\"w-2 h-2 bg-white/50 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\" />\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : (\n            /* Camera Off State */\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <div className=\"text-6xl mb-4\">📷</div>\n                <p className=\"text-lg\">Camera Inactive</p>\n                <p className=\"text-sm\">Click \"Activate Camera\" to start</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Progress Bar */}\n        {isScanning && (\n          <div className=\"mt-4\">\n            <div className=\"flex justify-between text-sm text-text-secondary mb-2\">\n              <span>AR Scanning Progress</span>\n              <span>{scanProgress}%</span>\n            </div>\n            <div className=\"w-full bg-border rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n                style={{ width: `${scanProgress}%` }}\n                transition={{ duration: 0.1 }}\n              />\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Instructions */}\n      <div className=\"text-center space-y-2\">\n        <p className=\"text-text-secondary\">\n          {!cameraActive \n            ? \"Activate the camera to simulate AR scanning\"\n            : !isScanning && !showCard\n            ? \"Click 'Start AR Scan' to detect the business card\"\n            : isScanning\n            ? \"Scanning for AR business cards...\"\n            : \"AR business card detected and displayed!\"\n          }\n        </p>\n        \n        {showCard && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"bg-accent-green/10 border border-accent-green/30 rounded-lg p-3 max-w-md mx-auto\"\n          >\n            <p className=\"text-accent-green text-sm\">\n              ✅ Success! The AR business card has been detected and is now floating in your camera view.\n            </p>\n          </motion.div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CameraSimulator;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAExB,MAAM,YAAY;QAChB,cAAc;QACd,gBAAgB;QAEhB,6BAA6B;QAC7B,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAA;gBACd,IAAI,QAAQ,KAAK;oBACf,cAAc;oBACd,cAAc;oBACd,YAAY;oBACZ;oBACA,OAAO;gBACT;gBACA,OAAO,OAAO;YAChB;QACF,GAAG;IACL;IAEA,MAAM,YAAY;QAChB,YAAY;QACZ,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,MAAM,QAAQ;QACZ,MAAM;QACN,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,YAAY,KAAK,CAAC,KAAK,SAAS,CAAC,IAAI,MAAM,IAAI;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,UAAM;wBACL,SAAS;wBACT,SAAS,eAAe,YAAY;wBACpC,UAAU;;4BACX;4BACK,eAAe,kBAAkB;;;;;;;kCAEvC,8OAAC,iIAAA,CAAA,UAAM;wBACL,SAAS;wBACT,UAAU,CAAC,gBAAgB;wBAC3B,SAAQ;;4BACT;4BACK,aAAa,gBAAgB;;;;;;;kCAEnC,8OAAC,iIAAA,CAAA,UAAM;wBACL,SAAS;wBACT,SAAQ;kCACT;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAEZ,6BACC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wDAEC,WAAU;wDACV,OAAO;4DACL,YAAY,KAAK,MAAM,KAAK,MAAM,2BAA2B;wDAC/D;uDAJK;;;;;;;;;;;;;;;sDAWb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;gCAKlB,4BACC;;sDAEE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,WAAU;wDACV,SAAS;4DACP,SAAS;gEAAC;gEAAG;gEAAG;6DAAE;wDACpB;wDACA,YAAY;4DACV,UAAU;4DACV,OAAO,IAAI;4DACX,QAAQ;4DACR,aAAa;wDACf;uDAVK;;;;;;;;;;;;;;;sDAiBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,OAAO;gDAAE,WAAW;4CAAmB;4CACvC,SAAS;gDACP,KAAK;oDAAC;oDAAM;iDAAO;4CACrB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;;;;;;sDAIF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;8CAMrB,8OAAC,yLAAA,CAAA,kBAAe;8CACb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;4CAAK,GAAG;wCAAG;wCACzC,SAAS;4CAAE,SAAS;4CAAG,OAAO;4CAAG,GAAG;wCAAE;wCACtC,MAAM;4CAAE,SAAS;4CAAG,OAAO;4CAAK,GAAG,CAAC;wCAAG;wCACvC,WAAU;;0DAGV,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,YAAY;oDACZ,aAAa;oDACb,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC;gDACtC;;kEAGA,8OAAC;wDAAI,WAAU;wDAAsE,OAAO;4DAAE,aAAa;wDAAU;;;;;;kEACrH,8OAAC;wDAAI,WAAU;wDAAuE,OAAO;4DAAE,aAAa;wDAAU;;;;;;kEACtH,8OAAC;wDAAI,WAAU;wDAAyE,OAAO;4DAAE,aAAa;wDAAU;;;;;;kEACxH,8OAAC;wDAAI,WAAU;wDAA0E,OAAO;4DAAE,aAAa;wDAAU;;;;;;kEAGzH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,YAAY;gFAAU;0FAE9B,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;0FAEhD,8OAAC;gFAAG,WAAU;0FAAgC,KAAK,QAAQ;;;;;;0FAC3D,8OAAC;gFAAE,WAAU;0FAAyB,KAAK,SAAS;;;;;;;;;;;;kFAEtD,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,aAAa,YAAY;4EAAM,OAAO;wEAAU;kFAC1D;;;;;;;;;;;;0EAKH,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,OAAO;oFAAE,OAAO;gFAAU;0FAAG;;;;;;0FACnC,8OAAC;0FAAM,KAAK,SAAS;;;;;;;;;;;;kFAEvB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAK,OAAO;oFAAE,OAAO;gFAAU;0FAAG;;;;;;0FACnC,8OAAC;0FAAM,KAAK,SAAS;;;;;;;;;;;;;;;;;;;;;;;;oDAM1B;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,WAAU;4DACV,OAAO;gEACL,YAAY;gEACZ,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;gEACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;4DAC9B;4DACA,SAAS;gEACP,GAAG;oEAAC,CAAC;oEAAI;oEAAI,CAAC;iEAAG;gEACjB,SAAS;oEAAC;oEAAK;oEAAG;iEAAI;4DACxB;4DACA,YAAY;gEACV,UAAU,IAAI,IAAI;gEAClB,QAAQ;gEACR,MAAM;4DACR;2DAfK;;;;;;;;;;;0DAqBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO;gDAAI;gDACzB,WAAU;;kEAEV,8OAAC;wDACC,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;8CAST,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAAiC;;;;;;;;;;;;8CAGnD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACZ,aAAa,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,GAAG,WAAW,mBAAmB;;;;;;;;;;;gCAKhF,gBAAgB,CAAC,0BAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;mCAMvB,oBAAoB,iBACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAE,WAAU;kDAAU;;;;;;kDACvB,8OAAC;wCAAE,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;oBAO9B,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;;4CAAM;4CAAa;;;;;;;;;;;;;0CAEtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,aAAa,CAAC,CAAC;oCAAC;oCACnC,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;;;;;;;0BAQtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCACV,CAAC,eACE,gDACA,CAAC,cAAc,CAAC,WAChB,sDACA,aACA,sCACA;;;;;;oBAIL,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;;;;;;;AAQrD;uCAEe", "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/DemoEngine.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from '../ui/Button';\nimport ARCardRenderer from './ARCardRenderer';\nimport CameraSimulator from './CameraSimulator';\nimport SharingDemo from './SharingDemo';\nimport AnalyticsDemo from './AnalyticsDemo';\n\nconst DemoLevel = ({ level, title, description, isActive, isCompleted, onClick, isLocked }) => (\n  <motion.div\n    whileHover={!isLocked ? { scale: 1.02 } : {}}\n    whileTap={!isLocked ? { scale: 0.98 } : {}}\n    className={`p-4 rounded-lg border cursor-pointer transition-all duration-300 ${\n      isActive \n        ? 'bg-primary-cyan/10 border-primary-cyan shadow-lg shadow-primary-cyan/25' \n        : isCompleted\n        ? 'bg-accent-green/10 border-accent-green'\n        : isLocked\n        ? 'bg-border/50 border-border opacity-50 cursor-not-allowed'\n        : 'bg-surface border-border hover:border-primary-cyan/50'\n    }`}\n    onClick={!isLocked ? onClick : undefined}\n  >\n    <div className=\"flex items-center space-x-3\">\n      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${\n        isCompleted \n          ? 'bg-accent-green text-black' \n          : isActive\n          ? 'bg-primary-cyan text-black'\n          : isLocked\n          ? 'bg-border text-text-muted'\n          : 'bg-surface border border-border text-text-secondary'\n      }`}>\n        {isCompleted ? '✓' : isLocked ? '🔒' : level}\n      </div>\n      <div className=\"flex-1\">\n        <h3 className={`font-semibold ${isLocked ? 'text-text-muted' : 'text-text-primary'}`}>\n          {title}\n        </h3>\n        <p className={`text-sm ${isLocked ? 'text-text-muted' : 'text-text-secondary'}`}>\n          {description}\n        </p>\n      </div>\n    </div>\n  </motion.div>\n);\n\nconst ProgressBar = ({ currentLevel, totalLevels }) => (\n  <div className=\"w-full bg-border rounded-full h-2 mb-6\">\n    <motion.div\n      className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n      initial={{ width: 0 }}\n      animate={{ width: `${(currentLevel / totalLevels) * 100}%` }}\n      transition={{ duration: 0.5 }}\n    />\n  </div>\n);\n\nconst InstructionOverlay = ({ instruction, onNext, onSkip, showSkip = true }) => (\n  <motion.div\n    initial={{ opacity: 0 }}\n    animate={{ opacity: 1 }}\n    exit={{ opacity: 0 }}\n    className=\"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\"\n  >\n    <motion.div\n      initial={{ scale: 0.8, opacity: 0 }}\n      animate={{ scale: 1, opacity: 1 }}\n      exit={{ scale: 0.8, opacity: 0 }}\n      className=\"bg-surface border border-border rounded-xl p-8 max-w-md mx-4 text-center\"\n    >\n      <div className=\"text-4xl mb-4\">💡</div>\n      <h3 className=\"text-xl font-bold text-text-primary mb-4\">Instructions</h3>\n      <p className=\"text-text-secondary mb-6\">{instruction}</p>\n      <div className=\"flex space-x-3 justify-center\">\n        <Button onClick={onNext}>Got it!</Button>\n        {showSkip && (\n          <Button variant=\"ghost\" onClick={onSkip}>Skip Tutorial</Button>\n        )}\n      </div>\n    </motion.div>\n  </motion.div>\n);\n\nexport default function DemoEngine() {\n  const [currentLevel, setCurrentLevel] = useState(1);\n  const [completedLevels, setCompletedLevels] = useState(new Set());\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [demoData, setDemoData] = useState({\n    cardStyle: 'modern',\n    userName: 'Alex Johnson',\n    userTitle: 'Senior Developer',\n    userCompany: 'TechCorp Inc.',\n    userEmail: '<EMAIL>',\n    userPhone: '+****************',\n    cardColor: 'cyan',\n    arEffect: 'glow',\n    scanCount: 0,\n    shareCount: 0\n  });\n\n  const levels = [\n    {\n      id: 1,\n      title: 'Basic AR Card',\n      description: 'View your first 3D business card',\n      instruction: 'Welcome! This is your AR business card. Hover over it to see the 3D effect in action.',\n      component: 'card'\n    },\n    {\n      id: 2,\n      title: 'AR Camera View',\n      description: 'See how it looks in AR camera mode',\n      instruction: 'Now let\\'s see how your card appears through AR camera. This simulates the real-world experience.',\n      component: 'camera'\n    },\n    {\n      id: 3,\n      title: 'Customize Your Card',\n      description: 'Change colors, effects, and information',\n      instruction: 'Customize your card! Try different colors, effects, and update your information.',\n      component: 'customize'\n    },\n    {\n      id: 4,\n      title: 'Sharing Methods',\n      description: 'Generate QR codes and sharing links',\n      instruction: 'Learn how to share your card. Generate QR codes, NFC simulation, and direct links.',\n      component: 'sharing'\n    },\n    {\n      id: 5,\n      title: 'Analytics Dashboard',\n      description: 'Track scans and engagement',\n      instruction: 'See how your networking performs with real-time analytics and engagement tracking.',\n      component: 'analytics'\n    }\n  ];\n\n  const currentLevelData = levels.find(level => level.id === currentLevel);\n\n  const completeLevel = (levelId) => {\n    setCompletedLevels(prev => new Set([...prev, levelId]));\n    if (levelId === currentLevel && levelId < levels.length) {\n      setTimeout(() => setCurrentLevel(levelId + 1), 1000);\n    }\n  };\n\n  const resetDemo = () => {\n    setCurrentLevel(1);\n    setCompletedLevels(new Set());\n    setShowInstructions(true);\n    setDemoData(prev => ({\n      ...prev,\n      scanCount: 0,\n      shareCount: 0\n    }));\n  };\n\n  const renderCurrentComponent = () => {\n    switch (currentLevelData?.component) {\n      case 'card':\n        return (\n          <ARCardRenderer \n            data={demoData} \n            onInteraction={() => completeLevel(1)}\n          />\n        );\n      case 'camera':\n        return (\n          <CameraSimulator \n            data={demoData} \n            onScan={() => {\n              setDemoData(prev => ({ ...prev, scanCount: prev.scanCount + 1 }));\n              completeLevel(2);\n            }}\n          />\n        );\n      case 'customize':\n        return (\n          <div className=\"space-y-6\">\n            <ARCardRenderer data={demoData} />\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {/* Color Selection */}\n              <div className=\"bg-surface border border-border rounded-lg p-4\">\n                <h4 className=\"font-semibold text-text-primary mb-3\">Card Color</h4>\n                <div className=\"grid grid-cols-4 gap-2\">\n                  {['cyan', 'purple', 'green', 'orange'].map(color => (\n                    <button\n                      key={color}\n                      onClick={() => {\n                        setDemoData(prev => ({ ...prev, cardColor: color }));\n                        completeLevel(3);\n                      }}\n                      className={`w-12 h-12 rounded-lg border-2 transition-all ${\n                        demoData.cardColor === color ? 'border-white scale-110' : 'border-transparent'\n                      }`}\n                      style={{\n                        background: color === 'cyan' ? '#00f5ff' : \n                                  color === 'purple' ? '#8b5cf6' :\n                                  color === 'green' ? '#10b981' : '#f59e0b'\n                      }}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* Effect Selection */}\n              <div className=\"bg-surface border border-border rounded-lg p-4\">\n                <h4 className=\"font-semibold text-text-primary mb-3\">AR Effect</h4>\n                <div className=\"space-y-2\">\n                  {['glow', 'float', 'pulse', 'spin'].map(effect => (\n                    <button\n                      key={effect}\n                      onClick={() => {\n                        setDemoData(prev => ({ ...prev, arEffect: effect }));\n                        completeLevel(3);\n                      }}\n                      className={`w-full text-left px-3 py-2 rounded border transition-all ${\n                        demoData.arEffect === effect \n                          ? 'border-primary-cyan bg-primary-cyan/10' \n                          : 'border-border hover:border-primary-cyan/50'\n                      }`}\n                    >\n                      {effect.charAt(0).toUpperCase() + effect.slice(1)}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        );\n      case 'sharing':\n        return (\n          <SharingDemo \n            data={demoData} \n            onShare={() => {\n              setDemoData(prev => ({ ...prev, shareCount: prev.shareCount + 1 }));\n              completeLevel(4);\n            }}\n          />\n        );\n      case 'analytics':\n        return (\n          <AnalyticsDemo \n            data={demoData} \n            onView={() => completeLevel(5)}\n          />\n        );\n      default:\n        return <div>Loading...</div>;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background py-12\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-hero gradient-text mb-6\">\n            Interactive AR Demo\n          </h1>\n          <p className=\"text-xl text-text-secondary mb-8 max-w-3xl mx-auto\">\n            Experience the complete NameCardAI journey. Go through each level to see how AR business cards revolutionize networking.\n          </p>\n          \n          {/* Progress */}\n          <div className=\"max-w-md mx-auto\">\n            <div className=\"flex justify-between text-sm text-text-secondary mb-2\">\n              <span>Progress</span>\n              <span>{completedLevels.size}/{levels.length} Complete</span>\n            </div>\n            <ProgressBar currentLevel={completedLevels.size} totalLevels={levels.length} />\n          </div>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-4 gap-8\">\n          {/* Level Navigation */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 sticky top-24\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-4\">Demo Levels</h3>\n              <div className=\"space-y-3\">\n                {levels.map((level) => (\n                  <DemoLevel\n                    key={level.id}\n                    level={level.id}\n                    title={level.title}\n                    description={level.description}\n                    isActive={currentLevel === level.id}\n                    isCompleted={completedLevels.has(level.id)}\n                    isLocked={level.id > Math.max(currentLevel, Math.max(...completedLevels) + 1)}\n                    onClick={() => setCurrentLevel(level.id)}\n                  />\n                ))}\n              </div>\n              \n              <div className=\"mt-6 pt-6 border-t border-border\">\n                <Button \n                  variant=\"outline\" \n                  size=\"sm\" \n                  onClick={resetDemo}\n                  className=\"w-full\"\n                >\n                  Reset Demo\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Demo Area */}\n          <div className=\"lg:col-span-3\">\n            <motion.div\n              key={currentLevel}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-8 min-h-[600px] relative\"\n            >\n              {/* Level Header */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-primary-cyan text-black rounded-full flex items-center justify-center font-bold\">\n                    {currentLevel}\n                  </div>\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-text-primary\">\n                      {currentLevelData?.title}\n                    </h2>\n                    <p className=\"text-text-secondary\">\n                      {currentLevelData?.description}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Demo Component */}\n              <div className=\"relative\">\n                {renderCurrentComponent()}\n              </div>\n\n              {/* Instructions Overlay */}\n              <AnimatePresence>\n                {showInstructions && currentLevelData && (\n                  <InstructionOverlay\n                    instruction={currentLevelData.instruction}\n                    onNext={() => setShowInstructions(false)}\n                    onSkip={() => setShowInstructions(false)}\n                  />\n                )}\n              </AnimatePresence>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Completion Message */}\n        <AnimatePresence>\n          {completedLevels.size === levels.length && (\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -50 }}\n              className=\"mt-12 text-center bg-gradient-to-r from-accent-green/10 to-primary-cyan/10 border border-accent-green/30 rounded-xl p-8\"\n            >\n              <div className=\"text-6xl mb-4\">🎉</div>\n              <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n                Demo Complete!\n              </h3>\n              <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n                Congratulations! You've experienced the full NameCardAI journey. Ready to create your own AR business card?\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" href=\"/signup\">\n                  Start Your AR Journey\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" onClick={resetDemo}>\n                  Try Demo Again\n                </Button>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;;;AANA;;;;;;;;;AAUA,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,iBACxF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;QAC3C,UAAU,CAAC,WAAW;YAAE,OAAO;QAAK,IAAI,CAAC;QACzC,WAAW,CAAC,iEAAiE,EAC3E,WACI,4EACA,cACA,2CACA,WACA,6DACA,yDACJ;QACF,SAAS,CAAC,WAAW,UAAU;kBAE/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,wEAAwE,EACvF,cACI,+BACA,WACA,+BACA,WACA,8BACA,uDACJ;8BACC,cAAc,MAAM,WAAW,OAAO;;;;;;8BAEzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAW,CAAC,cAAc,EAAE,WAAW,oBAAoB,qBAAqB;sCACjF;;;;;;sCAEH,8OAAC;4BAAE,WAAW,CAAC,QAAQ,EAAE,WAAW,oBAAoB,uBAAuB;sCAC5E;;;;;;;;;;;;;;;;;;;;;;;AAOX,MAAM,cAAc,CAAC,EAAE,YAAY,EAAE,WAAW,EAAE,iBAChD,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,OAAO;YAAE;YACpB,SAAS;gBAAE,OAAO,GAAG,AAAC,eAAe,cAAe,IAAI,CAAC,CAAC;YAAC;YAC3D,YAAY;gBAAE,UAAU;YAAI;;;;;;;;;;;AAKlC,MAAM,qBAAqB,CAAC,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,IAAI,EAAE,iBAC1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAU;kBAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAClC,SAAS;gBAAE,OAAO;gBAAG,SAAS;YAAE;YAChC,MAAM;gBAAE,OAAO;gBAAK,SAAS;YAAE;YAC/B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,8OAAC;oBAAG,WAAU;8BAA2C;;;;;;8BACzD,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;8BACzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,UAAM;4BAAC,SAAS;sCAAQ;;;;;;wBACxB,0BACC,8OAAC,iIAAA,CAAA,UAAM;4BAAC,SAAQ;4BAAQ,SAAS;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;AAOpC,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,WAAW;QACX,aAAa;QACb,WAAW;QACX,WAAW;QACX,WAAW;QACX,UAAU;QACV,WAAW;QACX,YAAY;IACd;IAEA,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,mBAAmB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE3D,MAAM,gBAAgB,CAAC;QACrB,mBAAmB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAQ;QACrD,IAAI,YAAY,gBAAgB,UAAU,OAAO,MAAM,EAAE;YACvD,WAAW,IAAM,gBAAgB,UAAU,IAAI;QACjD;IACF;IAEA,MAAM,YAAY;QAChB,gBAAgB;QAChB,mBAAmB,IAAI;QACvB,oBAAoB;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;gBACX,YAAY;YACd,CAAC;IACH;IAEA,MAAM,yBAAyB;QAC7B,OAAQ,kBAAkB;YACxB,KAAK;gBACH,qBACE,8OAAC,2IAAA,CAAA,UAAc;oBACb,MAAM;oBACN,eAAe,IAAM,cAAc;;;;;;YAGzC,KAAK;gBACH,qBACE,8OAAC,4IAAA,CAAA,UAAe;oBACd,MAAM;oBACN,QAAQ;wBACN,YAAY,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,WAAW,KAAK,SAAS,GAAG;4BAAE,CAAC;wBAC/D,cAAc;oBAChB;;;;;;YAGN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,2IAAA,CAAA,UAAc;4BAAC,MAAM;;;;;;sCACtB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAQ;gDAAU;gDAAS;6CAAS,CAAC,GAAG,CAAC,CAAA,sBACzC,8OAAC;oDAEC,SAAS;wDACP,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,WAAW;4DAAM,CAAC;wDAClD,cAAc;oDAChB;oDACA,WAAW,CAAC,6CAA6C,EACvD,SAAS,SAAS,KAAK,QAAQ,2BAA2B,sBAC1D;oDACF,OAAO;wDACL,YAAY,UAAU,SAAS,YACrB,UAAU,WAAW,YACrB,UAAU,UAAU,YAAY;oDAC5C;mDAZK;;;;;;;;;;;;;;;;8CAmBb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAQ;gDAAS;gDAAS;6CAAO,CAAC,GAAG,CAAC,CAAA,uBACtC,8OAAC;oDAEC,SAAS;wDACP,YAAY,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,UAAU;4DAAO,CAAC;wDAClD,cAAc;oDAChB;oDACA,WAAW,CAAC,yDAAyD,EACnE,SAAS,QAAQ,KAAK,SAClB,2CACA,8CACJ;8DAED,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;mDAX1C;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAmBrB,KAAK;gBACH,qBACE,8OAAC;oBACC,MAAM;oBACN,SAAS;wBACP,YAAY,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,YAAY,KAAK,UAAU,GAAG;4BAAE,CAAC;wBACjE,cAAc;oBAChB;;;;;;YAGN,KAAK;gBACH,qBACE,8OAAC;oBACC,MAAM;oBACN,QAAQ,IAAM,cAAc;;;;;;YAGlC;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;sCAKlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;gDAAM,gBAAgB,IAAI;gDAAC;gDAAE,OAAO,MAAM;gDAAC;;;;;;;;;;;;;8CAE9C,8OAAC;oCAAY,cAAc,gBAAgB,IAAI;oCAAE,aAAa,OAAO,MAAM;;;;;;;;;;;;;;;;;;8BAI/E,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;gDAEC,OAAO,MAAM,EAAE;gDACf,OAAO,MAAM,KAAK;gDAClB,aAAa,MAAM,WAAW;gDAC9B,UAAU,iBAAiB,MAAM,EAAE;gDACnC,aAAa,gBAAgB,GAAG,CAAC,MAAM,EAAE;gDACzC,UAAU,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,cAAc,KAAK,GAAG,IAAI,mBAAmB;gDAC3E,SAAS,IAAM,gBAAgB,MAAM,EAAE;+CAPlC,MAAM,EAAE;;;;;;;;;;kDAYnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,kBAAkB;;;;;;sEAErB,8OAAC;4DAAE,WAAU;sEACV,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;kDAO3B,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIH,8OAAC,yLAAA,CAAA,kBAAe;kDACb,oBAAoB,kCACnB,8OAAC;4CACC,aAAa,iBAAiB,WAAW;4CACzC,QAAQ,IAAM,oBAAoB;4CAClC,QAAQ,IAAM,oBAAoB;;;;;;;;;;;;+BAlCnC;;;;;;;;;;;;;;;;8BA2CX,8OAAC,yLAAA,CAAA,kBAAe;8BACb,gBAAgB,IAAI,KAAK,OAAO,MAAM,kBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,MAAK;kDAAU;;;;;;kDAGjC,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE", "debugId": null}}]}