{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,OAAO,kBACX,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;wBAAmC;sCACvC,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMpD,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;;;;kDACD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAKP,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;AAIU,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,8OAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,8OAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,8OAAC;;sEACC,8OAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,8OAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;4CAAG,WAAU;;gDAAkC;8DACtC,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/roadmap/RoadmapEngine.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport Button from '../ui/Button';\n\nexport default function RoadmapEngine() {\n  const [selectedQuarter, setSelectedQuarter] = useState('Q1-2024');\n  const timelineRef = useRef(null);\n  const isInView = useInView(timelineRef, { once: true });\n\n  const roadmapData = [\n    {\n      quarter: 'Q4-2023',\n      title: 'Foundation & Launch',\n      status: 'completed',\n      progress: 100,\n      features: [\n        { name: 'Core AR Card Engine', status: 'completed', description: 'Basic AR business card creation and viewing' },\n        { name: 'Mobile App MVP', status: 'completed', description: 'iOS and Android apps with core functionality' },\n        { name: 'Web Dashboard', status: 'completed', description: 'User management and card customization portal' },\n        { name: 'QR Code Integration', status: 'completed', description: 'Seamless QR code generation and scanning' }\n      ],\n      metrics: {\n        users: '10K+',\n        features: '15',\n        platforms: '3'\n      }\n    },\n    {\n      quarter: 'Q1-2024',\n      title: 'Enhanced Experience',\n      status: 'completed',\n      progress: 100,\n      features: [\n        { name: 'Advanced Analytics', status: 'completed', description: 'Comprehensive tracking and insights dashboard' },\n        { name: 'Team Management', status: 'completed', description: 'Enterprise features for team collaboration' },\n        { name: 'Custom Branding', status: 'completed', description: 'White-label solutions for businesses' },\n        { name: 'API Integration', status: 'completed', description: 'Third-party integrations and webhooks' }\n      ],\n      metrics: {\n        users: '100K+',\n        features: '25',\n        platforms: '5'\n      }\n    },\n    {\n      quarter: 'Q2-2024',\n      title: 'AI & Automation',\n      status: 'in-progress',\n      progress: 75,\n      features: [\n        { name: 'AI-Powered Recommendations', status: 'completed', description: 'Smart networking suggestions based on behavior' },\n        { name: 'Auto-Follow-up System', status: 'completed', description: 'Automated email sequences and reminders' },\n        { name: 'Smart Card Templates', status: 'in-progress', description: 'AI-generated card designs and layouts' },\n        { name: 'Voice Commands', status: 'planned', description: 'Voice-activated card sharing and management' }\n      ],\n      metrics: {\n        users: '500K+',\n        features: '35',\n        platforms: '7'\n      }\n    },\n    {\n      quarter: 'Q3-2024',\n      title: 'Advanced AR Features',\n      status: 'planned',\n      progress: 25,\n      features: [\n        { name: 'Holographic Displays', status: 'in-progress', description: '3D holographic card projections' },\n        { name: 'Gesture Recognition', status: 'planned', description: 'Hand gesture-based interactions' },\n        { name: 'Spatial Audio', status: 'planned', description: '3D audio experiences in AR space' },\n        { name: 'Multi-User AR Sessions', status: 'planned', description: 'Collaborative AR networking spaces' }\n      ],\n      metrics: {\n        users: '1M+',\n        features: '45',\n        platforms: '10'\n      }\n    },\n    {\n      quarter: 'Q4-2024',\n      title: 'Enterprise & Scale',\n      status: 'planned',\n      progress: 0,\n      features: [\n        { name: 'Enterprise SSO', status: 'planned', description: 'Single sign-on for large organizations' },\n        { name: 'Advanced Security', status: 'planned', description: 'End-to-end encryption and compliance' },\n        { name: 'Global CDN', status: 'planned', description: 'Worldwide content delivery network' },\n        { name: 'White-label Platform', status: 'planned', description: 'Complete rebrandable solution' }\n      ],\n      metrics: {\n        users: '2M+',\n        features: '60',\n        platforms: '15'\n      }\n    },\n    {\n      quarter: 'Q1-2025',\n      title: 'Next-Gen Innovation',\n      status: 'future',\n      progress: 0,\n      features: [\n        { name: 'Neural Interface', status: 'research', description: 'Brain-computer interface integration' },\n        { name: 'Quantum Encryption', status: 'research', description: 'Quantum-secured data transmission' },\n        { name: 'Metaverse Integration', status: 'planned', description: 'Virtual world networking experiences' },\n        { name: 'AI Avatars', status: 'planned', description: 'Intelligent digital representatives' }\n      ],\n      metrics: {\n        users: '5M+',\n        features: '100+',\n        platforms: '25+'\n      }\n    }\n  ];\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'completed': return 'text-accent-green';\n      case 'in-progress': return 'text-primary-cyan';\n      case 'planned': return 'text-accent-orange';\n      case 'research': return 'text-primary-purple';\n      default: return 'text-text-secondary';\n    }\n  };\n\n  const getStatusIcon = (status) => {\n    switch (status) {\n      case 'completed': return '✅';\n      case 'in-progress': return '🔄';\n      case 'planned': return '📋';\n      case 'research': return '🔬';\n      default: return '⏳';\n    }\n  };\n\n  const selectedData = roadmapData.find(item => item.quarter === selectedQuarter);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background\">\n      {/* Hero Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <motion.div\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-5xl md:text-7xl font-bold gradient-text mb-6\">\n              Product Roadmap\n            </h1>\n            <p className=\"text-xl md:text-2xl text-text-secondary mb-12 max-w-4xl mx-auto\">\n              See what's coming next for NameCardAI. We're building the future of networking, \n              one innovative feature at a time.\n            </p>\n          </motion.div>\n\n          {/* Current Status */}\n          <motion.div\n            className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          >\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <div className=\"text-3xl mb-2\">🚀</div>\n              <div className=\"text-2xl font-bold text-primary-cyan mb-1\">2.5M+</div>\n              <div className=\"text-text-secondary\">Active Users</div>\n            </div>\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <div className=\"text-3xl mb-2\">⚡</div>\n              <div className=\"text-2xl font-bold text-accent-green mb-1\">35+</div>\n              <div className=\"text-text-secondary\">Features Shipped</div>\n            </div>\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <div className=\"text-3xl mb-2\">🌍</div>\n              <div className=\"text-2xl font-bold text-accent-orange mb-1\">150+</div>\n              <div className=\"text-text-secondary\">Countries</div>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Timeline Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\" ref={timelineRef}>\n        <div className=\"max-w-6xl mx-auto\">\n          <motion.h2\n            className=\"text-4xl font-bold text-text-primary text-center mb-16\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={isInView ? { opacity: 1, y: 0 } : {}}\n            transition={{ duration: 0.8 }}\n          >\n            Development Timeline\n          </motion.h2>\n\n          {/* Quarter Selector */}\n          <div className=\"flex flex-wrap justify-center gap-2 mb-12\">\n            {roadmapData.map((item, index) => (\n              <motion.button\n                key={item.quarter}\n                onClick={() => setSelectedQuarter(item.quarter)}\n                className={`px-4 py-2 rounded-lg border transition-all ${\n                  selectedQuarter === item.quarter\n                    ? 'bg-primary-cyan text-black border-primary-cyan'\n                    : 'bg-surface border-border text-text-primary hover:border-primary-cyan/50'\n                }`}\n                initial={{ opacity: 0, y: 20 }}\n                animate={isInView ? { opacity: 1, y: 0 } : {}}\n                transition={{ delay: index * 0.1, duration: 0.5 }}\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                {item.quarter}\n              </motion.button>\n            ))}\n          </div>\n\n          {/* Timeline Visualization */}\n          <div className=\"relative mb-16\">\n            <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-border\" />\n            \n            {roadmapData.map((item, index) => (\n              <motion.div\n                key={item.quarter}\n                className={`relative flex items-center mb-12 ${\n                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'\n                }`}\n                initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}\n                animate={isInView ? { opacity: 1, x: 0 } : {}}\n                transition={{ delay: index * 0.2, duration: 0.8 }}\n              >\n                {/* Timeline Node */}\n                <div className=\"absolute left-1/2 transform -translate-x-1/2 w-6 h-6 rounded-full border-4 border-background bg-primary-cyan z-10\" />\n                \n                {/* Content Card */}\n                <div className={`w-5/12 ${index % 2 === 0 ? 'mr-auto pr-8' : 'ml-auto pl-8'}`}>\n                  <motion.div\n                    className={`bg-surface/50 backdrop-blur-sm border rounded-xl p-6 ${\n                      selectedQuarter === item.quarter\n                        ? 'border-primary-cyan shadow-lg shadow-primary-cyan/25'\n                        : 'border-border'\n                    }`}\n                    whileHover={{ scale: 1.02 }}\n                    onClick={() => setSelectedQuarter(item.quarter)}\n                  >\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <h3 className=\"text-xl font-bold text-text-primary\">{item.title}</h3>\n                      <span className={`text-2xl ${getStatusColor(item.status)}`}>\n                        {getStatusIcon(item.status)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-text-secondary mb-3\">{item.quarter}</div>\n                    \n                    {/* Progress Bar */}\n                    <div className=\"w-full bg-border rounded-full h-2 mb-4\">\n                      <motion.div\n                        className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n                        initial={{ width: 0 }}\n                        animate={isInView ? { width: `${item.progress}%` } : {}}\n                        transition={{ delay: index * 0.2 + 0.5, duration: 1 }}\n                      />\n                    </div>\n                    \n                    <div className=\"text-sm text-text-secondary\">\n                      {item.progress}% Complete • {item.features.length} Features\n                    </div>\n                  </motion.div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Detailed View */}\n      {selectedData && (\n        <section className=\"py-20 px-4 sm:px-6 lg:px-8 bg-surface/30\">\n          <div className=\"max-w-6xl mx-auto\">\n            <motion.div\n              key={selectedQuarter}\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5 }}\n            >\n              <div className=\"text-center mb-12\">\n                <h2 className=\"text-4xl font-bold text-text-primary mb-4\">\n                  {selectedData.title}\n                </h2>\n                <p className=\"text-xl text-text-secondary\">\n                  {selectedData.quarter} • {selectedData.progress}% Complete\n                </p>\n              </div>\n\n              {/* Metrics */}\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-12\">\n                <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 text-center\">\n                  <div className=\"text-3xl mb-2\">👥</div>\n                  <div className=\"text-2xl font-bold text-primary-cyan mb-1\">\n                    {selectedData.metrics.users}\n                  </div>\n                  <div className=\"text-text-secondary\">Target Users</div>\n                </div>\n                <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 text-center\">\n                  <div className=\"text-3xl mb-2\">⚡</div>\n                  <div className=\"text-2xl font-bold text-accent-green mb-1\">\n                    {selectedData.metrics.features}\n                  </div>\n                  <div className=\"text-text-secondary\">Total Features</div>\n                </div>\n                <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 text-center\">\n                  <div className=\"text-3xl mb-2\">🌐</div>\n                  <div className=\"text-2xl font-bold text-accent-orange mb-1\">\n                    {selectedData.metrics.platforms}\n                  </div>\n                  <div className=\"text-text-secondary\">Platforms</div>\n                </div>\n              </div>\n\n              {/* Features */}\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                {selectedData.features.map((feature, index) => (\n                  <motion.div\n                    key={feature.name}\n                    className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <h3 className=\"text-lg font-bold text-text-primary\">{feature.name}</h3>\n                      <span className={`text-lg ${getStatusColor(feature.status)}`}>\n                        {getStatusIcon(feature.status)}\n                      </span>\n                    </div>\n                    <p className=\"text-text-secondary text-sm mb-3\">{feature.description}</p>\n                    <div className={`text-xs font-semibold ${getStatusColor(feature.status)}`}>\n                      {feature.status.charAt(0).toUpperCase() + feature.status.slice(1).replace('-', ' ')}\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          </div>\n        </section>\n      )}\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          className=\"max-w-4xl mx-auto text-center bg-gradient-to-r from-primary-cyan/10 to-primary-purple/10 border border-primary-cyan/30 rounded-2xl p-12\"\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ duration: 0.8 }}\n        >\n          <h2 className=\"text-4xl font-bold text-text-primary mb-6\">\n            Shape the Future with Us\n          </h2>\n          <p className=\"text-xl text-text-secondary mb-8\">\n            Have ideas for features? Want to influence our roadmap? \n            Join our community and help build the future of networking.\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button size=\"lg\" href=\"/signup\">\n              🚀 Join Beta Program\n            </Button>\n            <Button variant=\"outline\" size=\"lg\">\n              💬 Share Feedback\n            </Button>\n          </div>\n        </motion.div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;IAAK;IAErD,MAAM,cAAc;QAClB;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAAuB,QAAQ;oBAAa,aAAa;gBAA8C;gBAC/G;oBAAE,MAAM;oBAAkB,QAAQ;oBAAa,aAAa;gBAA+C;gBAC3G;oBAAE,MAAM;oBAAiB,QAAQ;oBAAa,aAAa;gBAAgD;gBAC3G;oBAAE,MAAM;oBAAuB,QAAQ;oBAAa,aAAa;gBAA2C;aAC7G;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAAsB,QAAQ;oBAAa,aAAa;gBAAgD;gBAChH;oBAAE,MAAM;oBAAmB,QAAQ;oBAAa,aAAa;gBAA6C;gBAC1G;oBAAE,MAAM;oBAAmB,QAAQ;oBAAa,aAAa;gBAAuC;gBACpG;oBAAE,MAAM;oBAAmB,QAAQ;oBAAa,aAAa;gBAAwC;aACtG;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAA8B,QAAQ;oBAAa,aAAa;gBAAiD;gBACzH;oBAAE,MAAM;oBAAyB,QAAQ;oBAAa,aAAa;gBAA0C;gBAC7G;oBAAE,MAAM;oBAAwB,QAAQ;oBAAe,aAAa;gBAAwC;gBAC5G;oBAAE,MAAM;oBAAkB,QAAQ;oBAAW,aAAa;gBAA8C;aACzG;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAAwB,QAAQ;oBAAe,aAAa;gBAAkC;gBACtG;oBAAE,MAAM;oBAAuB,QAAQ;oBAAW,aAAa;gBAAkC;gBACjG;oBAAE,MAAM;oBAAiB,QAAQ;oBAAW,aAAa;gBAAmC;gBAC5F;oBAAE,MAAM;oBAA0B,QAAQ;oBAAW,aAAa;gBAAqC;aACxG;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAAkB,QAAQ;oBAAW,aAAa;gBAAyC;gBACnG;oBAAE,MAAM;oBAAqB,QAAQ;oBAAW,aAAa;gBAAuC;gBACpG;oBAAE,MAAM;oBAAc,QAAQ;oBAAW,aAAa;gBAAqC;gBAC3F;oBAAE,MAAM;oBAAwB,QAAQ;oBAAW,aAAa;gBAAgC;aACjG;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;QACA;YACE,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,UAAU;gBACR;oBAAE,MAAM;oBAAoB,QAAQ;oBAAY,aAAa;gBAAuC;gBACpG;oBAAE,MAAM;oBAAsB,QAAQ;oBAAY,aAAa;gBAAoC;gBACnG;oBAAE,MAAM;oBAAyB,QAAQ;oBAAW,aAAa;gBAAuC;gBACxG;oBAAE,MAAM;oBAAc,QAAQ;oBAAW,aAAa;gBAAsC;aAC7F;YACD,SAAS;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;YACb;QACF;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;IAE/D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;;8CAE5B,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAkE;;;;;;;;;;;;sCAOjF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;8CAEvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,8OAAC;4CAAI,WAAU;sDAA6C;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,8OAAC;gBAAQ,WAAU;gBAA6B,KAAK;0BACnD,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS,WAAW;gCAAE,SAAS;gCAAG,GAAG;4BAAE,IAAI,CAAC;4BAC5C,YAAY;gCAAE,UAAU;4BAAI;sCAC7B;;;;;;sCAKD,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,mBAAmB,KAAK,OAAO;oCAC9C,WAAW,CAAC,2CAA2C,EACrD,oBAAoB,KAAK,OAAO,GAC5B,mDACA,2EACJ;oCACF,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC5C,YAAY;wCAAE,OAAO,QAAQ;wCAAK,UAAU;oCAAI;oCAChD,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAEvB,KAAK,OAAO;mCAbR,KAAK,OAAO;;;;;;;;;;sCAmBvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;gCAEd,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAW,CAAC,iCAAiC,EAC3C,QAAQ,MAAM,IAAI,aAAa,oBAC/B;wCACF,SAAS;4CAAE,SAAS;4CAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;wCAAG;wCACrD,SAAS,WAAW;4CAAE,SAAS;4CAAG,GAAG;wCAAE,IAAI,CAAC;wCAC5C,YAAY;4CAAE,OAAO,QAAQ;4CAAK,UAAU;wCAAI;;0DAGhD,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAW,CAAC,OAAO,EAAE,QAAQ,MAAM,IAAI,iBAAiB,gBAAgB;0DAC3E,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAW,CAAC,qDAAqD,EAC/D,oBAAoB,KAAK,OAAO,GAC5B,yDACA,iBACJ;oDACF,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,SAAS,IAAM,mBAAmB,KAAK,OAAO;;sEAE9C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAuC,KAAK,KAAK;;;;;;8EAC/D,8OAAC;oEAAK,WAAW,CAAC,SAAS,EAAE,eAAe,KAAK,MAAM,GAAG;8EACvD,cAAc,KAAK,MAAM;;;;;;;;;;;;sEAI9B,8OAAC;4DAAI,WAAU;sEAAoC,KAAK,OAAO;;;;;;sEAG/D,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEAAE,OAAO;gEAAE;gEACpB,SAAS,WAAW;oEAAE,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC;gEAAC,IAAI,CAAC;gEACtD,YAAY;oEAAE,OAAO,QAAQ,MAAM;oEAAK,UAAU;gEAAE;;;;;;;;;;;sEAIxD,8OAAC;4DAAI,WAAU;;gEACZ,KAAK,QAAQ;gEAAC;gEAAc,KAAK,QAAQ,CAAC,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;uCA1CnD,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;YAqD1B,8BACC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,aAAa,KAAK;;;;;;kDAErB,8OAAC;wCAAE,WAAU;;4CACV,aAAa,OAAO;4CAAC;4CAAI,aAAa,QAAQ;4CAAC;;;;;;;;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DACZ,aAAa,OAAO,CAAC,KAAK;;;;;;0DAE7B,8OAAC;gDAAI,WAAU;0DAAsB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DACZ,aAAa,OAAO,CAAC,QAAQ;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;0DAAsB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DACZ,aAAa,OAAO,CAAC,SAAS;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;0DAAsB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;0CACZ,aAAa,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,QAAQ;wCAAI;wCACjC,YAAY;4CAAE,OAAO;wCAAK;;0DAE1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuC,QAAQ,IAAI;;;;;;kEACjE,8OAAC;wDAAK,WAAW,CAAC,QAAQ,EAAE,eAAe,QAAQ,MAAM,GAAG;kEACzD,cAAc,QAAQ,MAAM;;;;;;;;;;;;0DAGjC,8OAAC;gDAAE,WAAU;0DAAoC,QAAQ,WAAW;;;;;;0DACpE,8OAAC;gDAAI,WAAW,CAAC,sBAAsB,EAAE,eAAe,QAAQ,MAAM,GAAG;0DACtE,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK;;;;;;;uCAf5E,QAAQ,IAAI;;;;;;;;;;;uBA3ClB;;;;;;;;;;;;;;;0BAqEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAG1D,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;sCAIhD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,MAAK;8CAAU;;;;;;8CAGjC,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}]}