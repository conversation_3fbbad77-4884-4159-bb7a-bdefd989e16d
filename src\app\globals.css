@import "tailwindcss";

/* NameCardAI - Futuristic AI Theme */
:root {
  /* Core Colors */
  --background: #0a0a0a;
  --foreground: #ffffff;
  --surface: #1a1a1a;
  --border: #2a2a2a;

  /* AI-Inspired Palette */
  --primary-cyan: #00f5ff;
  --primary-purple: #8b5cf6;
  --accent-green: #10b981;
  --accent-orange: #f59e0b;
  --accent-pink: #ec4899;
  --accent-blue: #3b82f6;

  /* Text Colors */
  --text-primary: #ffffff;
  --text-secondary: #a1a1aa;
  --text-muted: #71717a;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #00f5ff, #8b5cf6);
  --gradient-accent: linear-gradient(135deg, #10b981, #f59e0b);
  --gradient-hero: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);

  /* Effects */
  --glow-cyan: 0 0 20px rgba(0, 245, 255, 0.3);
  --glow-purple: 0 0 20px rgba(139, 92, 246, 0.3);
  --glow-green: 0 0 20px rgba(16, 185, 129, 0.3);
}

@theme inline {
  /* Base Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-surface: var(--surface);
  --color-border: var(--border);

  /* Primary Colors */
  --color-primary-cyan: var(--primary-cyan);
  --color-primary-purple: var(--primary-purple);
  --color-accent-green: var(--accent-green);
  --color-accent-orange: var(--accent-orange);
  --color-accent-pink: var(--accent-pink);
  --color-accent-blue: var(--accent-blue);

  /* Text Colors */
  --color-text-primary: var(--text-primary);
  --color-text-secondary: var(--text-secondary);
  --color-text-muted: var(--text-muted);

  /* Fonts */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom Animations */
  --animate-glow: glow 2s ease-in-out infinite alternate;
  --animate-float: float 3s ease-in-out infinite;
  --animate-pulse-slow: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom Animations */
@keyframes glow {
  from { box-shadow: var(--glow-cyan); }
  to { box-shadow: var(--glow-purple); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes matrix-rain {
  0% { transform: translateY(-100vh); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  50% { border-color: transparent; }
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-purple);
}

/* Selection Styling */
::selection {
  background: var(--primary-cyan);
  color: var(--background);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--primary-cyan);
  outline-offset: 2px;
}

/* Utility Classes */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-effect {
  animation: var(--animate-glow);
}

.float-effect {
  animation: var(--animate-float);
}

.glass-effect {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.neon-border {
  border: 1px solid var(--primary-cyan);
  box-shadow: var(--glow-cyan);
}

/* Responsive Typography */
.text-hero {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
}

.text-section {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: 600;
  line-height: 1.2;
}
