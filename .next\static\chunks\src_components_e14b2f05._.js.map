{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAMA,MAAM,OAAO,kBACX,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,6LAAC;;8CACC,6LAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,6LAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,6LAAC;oBAAG,WAAU;;wBAAmC;sCACvC,6LAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;KA7E9C;AAmFN,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;;;;kDACD,6LAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;MA1EM;AA4ES,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;;;;;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,6LAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,6LAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E;GAhFwB;MAAA", "debugId": null}}, {"offset": {"line": 551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;KAXD;AAgBN,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;MAPC;AAWS,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,6LAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,6LAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,6LAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,6LAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,6LAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,6LAAC;;sEACC,6LAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,6LAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,6LAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,6LAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,6LAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,6LAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,6LAAC;4CAAG,WAAU;;gDAAkC;8DACtC,6LAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,6LAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,6LAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,6LAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,6LAAC;0DACC,cAAA,6LAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,6LAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,6LAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,6LAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;MAlLwB", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,6LAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,6LAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,6LAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/signup/SignUpEngine.js"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from '../ui/Button';\n\nexport default function SignUpEngine() {\n  const [step, setStep] = useState(1);\n  const [formData, setFormData] = useState({\n    firstName: '',\n    lastName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    company: '',\n    title: '',\n    industry: '',\n    plan: 'pro',\n    agreeToTerms: false,\n    subscribeNewsletter: true\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSuccess, setIsSuccess] = useState(false);\n\n  const plans = [\n    {\n      id: 'free',\n      name: 'Free',\n      price: '$0',\n      period: 'forever',\n      features: [\n        '1 AR Business Card',\n        'Basic Analytics',\n        'QR Code Sharing',\n        'Mobile App Access',\n        'Community Support'\n      ],\n      popular: false\n    },\n    {\n      id: 'pro',\n      name: 'Pro',\n      price: '$9.99',\n      period: 'per month',\n      features: [\n        'Unlimited AR Cards',\n        'Advanced Analytics',\n        'Custom Branding',\n        'Team Collaboration',\n        'Priority Support',\n        'API Access'\n      ],\n      popular: true\n    },\n    {\n      id: 'enterprise',\n      name: 'Enterprise',\n      price: 'Custom',\n      period: 'contact us',\n      features: [\n        'Everything in Pro',\n        'White-label Solution',\n        'Enterprise SSO',\n        'Advanced Security',\n        'Dedicated Support',\n        'Custom Integrations'\n      ],\n      popular: false\n    }\n  ];\n\n  const industries = [\n    'Technology', 'Healthcare', 'Finance', 'Education', 'Real Estate',\n    'Marketing', 'Sales', 'Consulting', 'Manufacturing', 'Retail',\n    'Legal', 'Non-profit', 'Government', 'Other'\n  ];\n\n  const validateStep = (stepNumber) => {\n    const newErrors = {};\n\n    if (stepNumber === 1) {\n      if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';\n      if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';\n      if (!formData.email.trim()) {\n        newErrors.email = 'Email is required';\n      } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n        newErrors.email = 'Email is invalid';\n      }\n    }\n\n    if (stepNumber === 2) {\n      if (!formData.password) {\n        newErrors.password = 'Password is required';\n      } else if (formData.password.length < 8) {\n        newErrors.password = 'Password must be at least 8 characters';\n      }\n      if (formData.password !== formData.confirmPassword) {\n        newErrors.confirmPassword = 'Passwords do not match';\n      }\n    }\n\n    if (stepNumber === 4) {\n      if (!formData.agreeToTerms) {\n        newErrors.agreeToTerms = 'You must agree to the terms and conditions';\n      }\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const nextStep = () => {\n    if (validateStep(step)) {\n      setStep(step + 1);\n    }\n  };\n\n  const prevStep = () => {\n    setStep(step - 1);\n    setErrors({});\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    if (!validateStep(step)) return;\n\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    setIsSuccess(true);\n  };\n\n  const updateFormData = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  if (isSuccess) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background flex items-center justify-center px-4\">\n        <motion.div\n          className=\"max-w-md w-full text-center\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.8 }}\n        >\n          <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-2xl p-8\">\n            <motion.div\n              className=\"text-6xl mb-6\"\n              animate={{ \n                scale: [1, 1.2, 1],\n                rotate: [0, 10, -10, 0]\n              }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              🎉\n            </motion.div>\n            \n            <h1 className=\"text-3xl font-bold text-text-primary mb-4\">\n              Welcome to NameCardAI!\n            </h1>\n            \n            <p className=\"text-text-secondary mb-8\">\n              Your account has been created successfully. Check your email for verification instructions.\n            </p>\n            \n            <div className=\"space-y-4\">\n              <Button size=\"lg\" href=\"/demo\" className=\"w-full\">\n                🚀 Try the Demo\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" className=\"w-full\">\n                📧 Resend Email\n              </Button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <h1 className=\"text-4xl md:text-5xl font-bold gradient-text mb-4\">\n            Start Your AR Journey\n          </h1>\n          <p className=\"text-xl text-text-secondary\">\n            Join 2.5M+ professionals revolutionizing networking\n          </p>\n        </motion.div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-12\">\n          <div className=\"flex justify-between text-sm text-text-secondary mb-2\">\n            <span>Step {step} of 4</span>\n            <span>{Math.round((step / 4) * 100)}% Complete</span>\n          </div>\n          <div className=\"w-full bg-border rounded-full h-2\">\n            <motion.div\n              className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n              initial={{ width: 0 }}\n              animate={{ width: `${(step / 4) * 100}%` }}\n              transition={{ duration: 0.5 }}\n            />\n          </div>\n        </div>\n\n        {/* Form Container */}\n        <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-2xl p-8\">\n          <form onSubmit={handleSubmit}>\n            <AnimatePresence mode=\"wait\">\n              {/* Step 1: Personal Information */}\n              {step === 1 && (\n                <motion.div\n                  key=\"step1\"\n                  initial={{ opacity: 0, x: 50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: -50 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  <h2 className=\"text-2xl font-bold text-text-primary mb-6\">Personal Information</h2>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                    <div>\n                      <label className=\"block text-text-primary font-medium mb-2\">First Name</label>\n                      <input\n                        type=\"text\"\n                        value={formData.firstName}\n                        onChange={(e) => updateFormData('firstName', e.target.value)}\n                        className={`w-full px-4 py-3 bg-background border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent ${\n                          errors.firstName ? 'border-red-400' : 'border-border'\n                        }`}\n                        placeholder=\"Enter your first name\"\n                      />\n                      {errors.firstName && <p className=\"text-red-400 text-sm mt-1\">{errors.firstName}</p>}\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-text-primary font-medium mb-2\">Last Name</label>\n                      <input\n                        type=\"text\"\n                        value={formData.lastName}\n                        onChange={(e) => updateFormData('lastName', e.target.value)}\n                        className={`w-full px-4 py-3 bg-background border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent ${\n                          errors.lastName ? 'border-red-400' : 'border-border'\n                        }`}\n                        placeholder=\"Enter your last name\"\n                      />\n                      {errors.lastName && <p className=\"text-red-400 text-sm mt-1\">{errors.lastName}</p>}\n                    </div>\n                  </div>\n                  \n                  <div className=\"mb-6\">\n                    <label className=\"block text-text-primary font-medium mb-2\">Email Address</label>\n                    <input\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={(e) => updateFormData('email', e.target.value)}\n                      className={`w-full px-4 py-3 bg-background border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent ${\n                        errors.email ? 'border-red-400' : 'border-border'\n                      }`}\n                      placeholder=\"Enter your email address\"\n                    />\n                    {errors.email && <p className=\"text-red-400 text-sm mt-1\">{errors.email}</p>}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Step 2: Security */}\n              {step === 2 && (\n                <motion.div\n                  key=\"step2\"\n                  initial={{ opacity: 0, x: 50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: -50 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  <h2 className=\"text-2xl font-bold text-text-primary mb-6\">Create Password</h2>\n                  \n                  <div className=\"mb-6\">\n                    <label className=\"block text-text-primary font-medium mb-2\">Password</label>\n                    <input\n                      type=\"password\"\n                      value={formData.password}\n                      onChange={(e) => updateFormData('password', e.target.value)}\n                      className={`w-full px-4 py-3 bg-background border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent ${\n                        errors.password ? 'border-red-400' : 'border-border'\n                      }`}\n                      placeholder=\"Create a strong password\"\n                    />\n                    {errors.password && <p className=\"text-red-400 text-sm mt-1\">{errors.password}</p>}\n                  </div>\n                  \n                  <div className=\"mb-6\">\n                    <label className=\"block text-text-primary font-medium mb-2\">Confirm Password</label>\n                    <input\n                      type=\"password\"\n                      value={formData.confirmPassword}\n                      onChange={(e) => updateFormData('confirmPassword', e.target.value)}\n                      className={`w-full px-4 py-3 bg-background border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent ${\n                        errors.confirmPassword ? 'border-red-400' : 'border-border'\n                      }`}\n                      placeholder=\"Confirm your password\"\n                    />\n                    {errors.confirmPassword && <p className=\"text-red-400 text-sm mt-1\">{errors.confirmPassword}</p>}\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Step 3: Professional Info */}\n              {step === 3 && (\n                <motion.div\n                  key=\"step3\"\n                  initial={{ opacity: 0, x: 50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: -50 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  <h2 className=\"text-2xl font-bold text-text-primary mb-6\">Professional Information</h2>\n                  \n                  <div className=\"grid md:grid-cols-2 gap-6 mb-6\">\n                    <div>\n                      <label className=\"block text-text-primary font-medium mb-2\">Company</label>\n                      <input\n                        type=\"text\"\n                        value={formData.company}\n                        onChange={(e) => updateFormData('company', e.target.value)}\n                        className=\"w-full px-4 py-3 bg-background border border-border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                        placeholder=\"Your company name\"\n                      />\n                    </div>\n                    \n                    <div>\n                      <label className=\"block text-text-primary font-medium mb-2\">Job Title</label>\n                      <input\n                        type=\"text\"\n                        value={formData.title}\n                        onChange={(e) => updateFormData('title', e.target.value)}\n                        className=\"w-full px-4 py-3 bg-background border border-border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                        placeholder=\"Your job title\"\n                      />\n                    </div>\n                  </div>\n                  \n                  <div className=\"mb-6\">\n                    <label className=\"block text-text-primary font-medium mb-2\">Industry</label>\n                    <select\n                      value={formData.industry}\n                      onChange={(e) => updateFormData('industry', e.target.value)}\n                      className=\"w-full px-4 py-3 bg-background border border-border rounded-lg text-text-primary focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                    >\n                      <option value=\"\">Select your industry</option>\n                      {industries.map(industry => (\n                        <option key={industry} value={industry}>{industry}</option>\n                      ))}\n                    </select>\n                  </div>\n\n                  {/* Plan Selection */}\n                  <div className=\"mb-6\">\n                    <label className=\"block text-text-primary font-medium mb-4\">Choose Your Plan</label>\n                    <div className=\"grid md:grid-cols-3 gap-4\">\n                      {plans.map(plan => (\n                        <motion.div\n                          key={plan.id}\n                          className={`relative border rounded-xl p-4 cursor-pointer transition-all ${\n                            formData.plan === plan.id\n                              ? 'border-primary-cyan bg-primary-cyan/10'\n                              : 'border-border hover:border-primary-cyan/50'\n                          }`}\n                          onClick={() => updateFormData('plan', plan.id)}\n                          whileHover={{ scale: 1.02 }}\n                          whileTap={{ scale: 0.98 }}\n                        >\n                          {plan.popular && (\n                            <div className=\"absolute -top-2 left-1/2 transform -translate-x-1/2 bg-primary-cyan text-black px-3 py-1 rounded-full text-xs font-bold\">\n                              Most Popular\n                            </div>\n                          )}\n                          \n                          <div className=\"text-center\">\n                            <h3 className=\"font-bold text-text-primary mb-2\">{plan.name}</h3>\n                            <div className=\"text-2xl font-bold text-primary-cyan mb-1\">{plan.price}</div>\n                            <div className=\"text-xs text-text-secondary mb-4\">{plan.period}</div>\n                            \n                            <ul className=\"text-xs text-text-secondary space-y-1\">\n                              {plan.features.slice(0, 3).map(feature => (\n                                <li key={feature}>✓ {feature}</li>\n                              ))}\n                              {plan.features.length > 3 && (\n                                <li>+ {plan.features.length - 3} more</li>\n                              )}\n                            </ul>\n                          </div>\n                        </motion.div>\n                      ))}\n                    </div>\n                  </div>\n                </motion.div>\n              )}\n\n              {/* Step 4: Terms & Confirmation */}\n              {step === 4 && (\n                <motion.div\n                  key=\"step4\"\n                  initial={{ opacity: 0, x: 50 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: -50 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  <h2 className=\"text-2xl font-bold text-text-primary mb-6\">Almost Done!</h2>\n                  \n                  <div className=\"bg-background border border-border rounded-lg p-6 mb-6\">\n                    <h3 className=\"font-bold text-text-primary mb-4\">Account Summary</h3>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-text-secondary\">Name:</span>\n                        <span className=\"text-text-primary\">{formData.firstName} {formData.lastName}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-text-secondary\">Email:</span>\n                        <span className=\"text-text-primary\">{formData.email}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-text-secondary\">Plan:</span>\n                        <span className=\"text-primary-cyan font-semibold\">\n                          {plans.find(p => p.id === formData.plan)?.name}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"space-y-4 mb-6\">\n                    <label className=\"flex items-start space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.agreeToTerms}\n                        onChange={(e) => updateFormData('agreeToTerms', e.target.checked)}\n                        className=\"mt-1 w-4 h-4 text-primary-cyan bg-background border-border rounded focus:ring-primary-cyan focus:ring-2\"\n                      />\n                      <span className=\"text-sm text-text-primary\">\n                        I agree to the <a href=\"#\" className=\"text-primary-cyan hover:underline\">Terms of Service</a> and <a href=\"#\" className=\"text-primary-cyan hover:underline\">Privacy Policy</a>\n                      </span>\n                    </label>\n                    {errors.agreeToTerms && <p className=\"text-red-400 text-sm\">{errors.agreeToTerms}</p>}\n                    \n                    <label className=\"flex items-start space-x-3\">\n                      <input\n                        type=\"checkbox\"\n                        checked={formData.subscribeNewsletter}\n                        onChange={(e) => updateFormData('subscribeNewsletter', e.target.checked)}\n                        className=\"mt-1 w-4 h-4 text-primary-cyan bg-background border-border rounded focus:ring-primary-cyan focus:ring-2\"\n                      />\n                      <span className=\"text-sm text-text-primary\">\n                        Subscribe to our newsletter for product updates and networking tips\n                      </span>\n                    </label>\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between pt-6\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={prevStep}\n                disabled={step === 1}\n                className={step === 1 ? 'invisible' : ''}\n              >\n                ← Previous\n              </Button>\n              \n              {step < 4 ? (\n                <Button type=\"button\" onClick={nextStep}>\n                  Next →\n                </Button>\n              ) : (\n                <Button type=\"submit\" disabled={isSubmitting}>\n                  {isSubmitting ? (\n                    <span className=\"flex items-center space-x-2\">\n                      <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                      <span>Creating Account...</span>\n                    </span>\n                  ) : (\n                    '🚀 Create Account'\n                  )}\n                </Button>\n              )}\n            </div>\n          </form>\n        </div>\n\n        {/* Trust Indicators */}\n        <motion.div\n          className=\"mt-12 text-center\"\n          initial={{ opacity: 0, y: 30 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.5, duration: 0.8 }}\n        >\n          <div className=\"flex items-center justify-center space-x-8 text-sm text-text-secondary\">\n            <span className=\"flex items-center space-x-2\">\n              <span>🔒</span>\n              <span>SSL Encrypted</span>\n            </span>\n            <span className=\"flex items-center space-x-2\">\n              <span>✅</span>\n              <span>GDPR Compliant</span>\n            </span>\n            <span className=\"flex items-center space-x-2\">\n              <span>🛡️</span>\n              <span>SOC 2 Certified</span>\n            </span>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,SAAS;QACT,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;QACd,qBAAqB;IACvB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,QAAQ;YACR,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;QACX;KACD;IAED,MAAM,aAAa;QACjB;QAAc;QAAc;QAAW;QAAa;QACpD;QAAa;QAAS;QAAc;QAAiB;QACrD;QAAS;QAAc;QAAc;KACtC;IAED,MAAM,eAAe,CAAC;QACpB,MAAM,YAAY,CAAC;QAEnB,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI,UAAU,SAAS,GAAG;YACtD,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI,UAAU,QAAQ,GAAG;YACpD,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;gBAC1B,UAAU,KAAK,GAAG;YACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;gBAC/C,UAAU,KAAK,GAAG;YACpB;QACF;QAEA,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,UAAU,QAAQ,GAAG;YACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACvC,UAAU,QAAQ,GAAG;YACvB;YACA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;gBAClD,UAAU,eAAe,GAAG;YAC9B;QACF;QAEA,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,SAAS,YAAY,EAAE;gBAC1B,UAAU,YAAY,GAAG;YAC3B;QACF;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,WAAW;QACf,IAAI,aAAa,OAAO;YACtB,QAAQ,OAAO;QACjB;IACF;IAEA,MAAM,WAAW;QACf,QAAQ,OAAO;QACf,UAAU,CAAC;IACb;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,aAAa,OAAO;QAEzB,gBAAgB;QAEhB,oBAAoB;QACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC,OAAO;QAC7B,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QAC7C;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;gCAClB,QAAQ;oCAAC;oCAAG;oCAAI,CAAC;oCAAI;iCAAE;4BACzB;4BACA,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;sCAC7C;;;;;;sCAID,6LAAC;4BAAG,WAAU;sCAA4C;;;;;;sCAI1D,6LAAC;4BAAE,WAAU;sCAA2B;;;;;;sCAIxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,UAAM;oCAAC,MAAK;oCAAK,MAAK;oCAAQ,WAAU;8CAAS;;;;;;8CAGlD,6LAAC,oIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQnE;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAGlE,6LAAC;4BAAE,WAAU;sCAA8B;;;;;;;;;;;;8BAM7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAK;wCAAM;wCAAK;;;;;;;8CACjB,6LAAC;;wCAAM,KAAK,KAAK,CAAC,AAAC,OAAO,IAAK;wCAAK;;;;;;;;;;;;;sCAEtC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO,GAAG,AAAC,OAAO,IAAK,IAAI,CAAC,CAAC;gCAAC;gCACzC,YAAY;oCAAE,UAAU;gCAAI;;;;;;;;;;;;;;;;;8BAMlC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAK,UAAU;;0CACd,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;;oCAEnB,SAAS,mBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA2C;;;;;;0EAC5D,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC3D,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,SAAS,GAAG,mBAAmB,iBACtC;gEACF,aAAY;;;;;;4DAEb,OAAO,SAAS,kBAAI,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,SAAS;;;;;;;;;;;;kEAGjF,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA2C;;;;;;0EAC5D,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC1D,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,QAAQ,GAAG,mBAAmB,iBACrC;gEACF,aAAY;;;;;;4DAEb,OAAO,QAAQ,kBAAI,6LAAC;gEAAE,WAAU;0EAA6B,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0DAIjF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2C;;;;;;kEAC5D,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;wDACvD,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,KAAK,GAAG,mBAAmB,iBAClC;wDACF,aAAY;;;;;;oDAEb,OAAO,KAAK,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK;;;;;;;;;;;;;uCAjDrE;;;;;oCAuDP,SAAS,mBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2C;;;;;;kEAC5D,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,QAAQ,GAAG,mBAAmB,iBACrC;wDACF,aAAY;;;;;;oDAEb,OAAO,QAAQ,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,QAAQ;;;;;;;;;;;;0DAG/E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2C;;;;;;kEAC5D,6LAAC;wDACC,MAAK;wDACL,OAAO,SAAS,eAAe;wDAC/B,UAAU,CAAC,IAAM,eAAe,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACjE,WAAW,CAAC,oJAAoJ,EAC9J,OAAO,eAAe,GAAG,mBAAmB,iBAC5C;wDACF,aAAY;;;;;;oDAEb,OAAO,eAAe,kBAAI,6LAAC;wDAAE,WAAU;kEAA6B,OAAO,eAAe;;;;;;;;;;;;;uCAjCzF;;;;;oCAuCP,SAAS,mBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA2C;;;;;;0EAC5D,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;gEACzD,WAAU;gEACV,aAAY;;;;;;;;;;;;kEAIhB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAA2C;;;;;;0EAC5D,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;gEACvD,WAAU;gEACV,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2C;;;;;;kEAC5D,6LAAC;wDACC,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC1D,WAAU;;0EAEV,6LAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;oEAAsB,OAAO;8EAAW;mEAA5B;;;;;;;;;;;;;;;;;0DAMnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;kEAA2C;;;;;;kEAC5D,6LAAC;wDAAI,WAAU;kEACZ,MAAM,GAAG,CAAC,CAAA,qBACT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEAET,WAAW,CAAC,6DAA6D,EACvE,SAAS,IAAI,KAAK,KAAK,EAAE,GACrB,2CACA,8CACJ;gEACF,SAAS,IAAM,eAAe,QAAQ,KAAK,EAAE;gEAC7C,YAAY;oEAAE,OAAO;gEAAK;gEAC1B,UAAU;oEAAE,OAAO;gEAAK;;oEAEvB,KAAK,OAAO,kBACX,6LAAC;wEAAI,WAAU;kFAA0H;;;;;;kFAK3I,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAG,WAAU;0FAAoC,KAAK,IAAI;;;;;;0FAC3D,6LAAC;gFAAI,WAAU;0FAA6C,KAAK,KAAK;;;;;;0FACtE,6LAAC;gFAAI,WAAU;0FAAoC,KAAK,MAAM;;;;;;0FAE9D,6LAAC;gFAAG,WAAU;;oFACX,KAAK,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,wBAC7B,6LAAC;;gGAAiB;gGAAG;;2FAAZ;;;;;oFAEV,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACtB,6LAAC;;4FAAG;4FAAG,KAAK,QAAQ,CAAC,MAAM,GAAG;4FAAE;;;;;;;;;;;;;;;;;;;;+DA1BjC,KAAK,EAAE;;;;;;;;;;;;;;;;;uCApDhB;;;;;oCA0FP,SAAS,mBACR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;;0DAE5B,6LAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,6LAAC;wEAAK,WAAU;;4EAAqB,SAAS,SAAS;4EAAC;4EAAE,SAAS,QAAQ;;;;;;;;;;;;;0EAE7E,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,6LAAC;wEAAK,WAAU;kFAAqB,SAAS,KAAK;;;;;;;;;;;;0EAErD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAsB;;;;;;kFACtC,6LAAC;wEAAK,WAAU;kFACb,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,IAAI,GAAG;;;;;;;;;;;;;;;;;;;;;;;;0DAMlD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,SAAS,YAAY;gEAC9B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,OAAO;gEAChE,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;;oEAA4B;kFAC3B,6LAAC;wEAAE,MAAK;wEAAI,WAAU;kFAAoC;;;;;;oEAAoB;kFAAK,6LAAC;wEAAE,MAAK;wEAAI,WAAU;kFAAoC;;;;;;;;;;;;;;;;;;oDAG/J,OAAO,YAAY,kBAAI,6LAAC;wDAAE,WAAU;kEAAwB,OAAO,YAAY;;;;;;kEAEhF,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,SAAS,mBAAmB;gEACrC,UAAU,CAAC,IAAM,eAAe,uBAAuB,EAAE,MAAM,CAAC,OAAO;gEACvE,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;;uCAjD5C;;;;;;;;;;;0CA2DV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU,SAAS;wCACnB,WAAW,SAAS,IAAI,cAAc;kDACvC;;;;;;oCAIA,OAAO,kBACN,6LAAC,oIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,SAAS;kDAAU;;;;;6DAIzC,6LAAC,oIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,6BACC,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;mDAGR;;;;;;;;;;;;;;;;;;;;;;;8BASZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;8BAExC,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;kDAAK;;;;;;kDACN,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;GA9gBwB;KAAA", "debugId": null}}]}