{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6feb203d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_6feb203d-module__8DQF1a__className\",\n  \"variable\": \"geist_6feb203d-module__8DQF1a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_6feb203d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_c7d183a.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_c7d183a-module__ZW1U4G__className\",\n  \"variable\": \"geist_mono_c7d183a-module__ZW1U4G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_c7d183a.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,yJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,yJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,yJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/app/layout.js"], "sourcesContent": ["import { Geist, <PERSON>eist_Mono } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata = {\n  title: \"NameCardAI - Your Name. Reinvented.\",\n  description: \"AR-enhanced digital business cards that let you share stunning, interactive profiles via QR, NFC, facial recognition, or camera scan—no app required.\",\n  keywords: \"digital business cards, AR business cards, QR code, NFC, networking, professional cards, 3D cards\",\n  authors: [{ name: \"NameCardAI Team\" }],\n  creator: \"<PERSON><PERSON>ard<PERSON><PERSON>\",\n  publisher: \"NameCardAI\",\n  openGraph: {\n    title: \"NameCardAI - Your Name. Reinvented.\",\n    description: \"AR-enhanced digital business cards that work everywhere. Connect in 3D. Remember Forever.\",\n    url: \"https://namecardai.com\",\n    siteName: \"NameCardAI\",\n    images: [\n      {\n        url: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n        width: 1200,\n        height: 630,\n        alt: \"NameCardAI - AR Business Cards\",\n      },\n    ],\n    locale: \"en_US\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"NameCardAI - Your Name. Reinvented.\",\n    description: \"AR-enhanced digital business cards that work everywhere\",\n    images: [\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\"],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  icons: {\n    icon: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    shortcut: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    apple: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n  },\n  manifest: \"/manifest.json\",\n};\n\nexport const viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n  maximumScale: 1,\n  themeColor: \"#00f5ff\",\n  colorScheme: \"dark\",\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" className=\"dark\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <meta name=\"theme-color\" content=\"#00f5ff\" />\n        <meta name=\"msapplication-TileColor\" content=\"#00f5ff\" />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-background text-foreground`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAaO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;QAAkB;KAAE;IACtC,SAAS;IACT,WAAW;IACX,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAA4F;IACvG;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,UAAU;AACZ;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;IACd,cAAc;IACd,YAAY;IACZ,aAAa;AACf;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;;;;;;;0BAE/C,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,6IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,0CAA0C,CAAC;0BAEjG;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}