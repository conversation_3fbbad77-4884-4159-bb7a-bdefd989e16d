/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Game Ready Event.
 *
 * This event is dispatched when the Phaser Game instance has finished booting, the Texture Manager is fully ready,
 * and all local systems are now able to start.
 *
 * @event Phaser.Core.Events#READY
 * @type {string}
 * @since 3.0.0
 */
module.exports = 'ready';
