/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Renderer Resize Event.
 *
 * This event is dispatched by the Phaser Renderer when it is resized, usually as a result
 * of the Scale Manager resizing.
 *
 * @event Phaser.Renderer.Events#RESIZE
 * @type {string}
 * @since 3.50.0
 *
 * @param {number} width - The new width of the renderer.
 * @param {number} height - The new height of the renderer.
 */
module.exports = 'resize';
