'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Button from '../ui/Button';

export default function DemoEngine() {
  const [currentLevel, setCurrentLevel] = useState(1);
  const [completedLevels, setCompletedLevels] = useState(new Set());
  const [showInstructions, setShowInstructions] = useState(true);
  const [demoData, setDemoData] = useState({
    userName: '<PERSON>',
    userTitle: 'Senior Developer',
    userCompany: 'TechCorp Inc.',
    userEmail: '<EMAIL>',
    userPhone: '+****************',
    cardColor: 'cyan',
    scanCount: 0,
    shareCount: 0
  });

  const levels = [
    {
      id: 1,
      title: 'Basic AR Card',
      description: 'View your first 3D business card',
      instruction: 'Welcome! This is your AR business card. Click on it to see the 3D effect in action.',
    },
    {
      id: 2,
      title: 'AR Camera View',
      description: 'See how it looks in AR camera mode',
      instruction: 'Now let\'s see how your card appears through AR camera. This simulates the real-world experience.',
    },
    {
      id: 3,
      title: 'Customize Your Card',
      description: 'Change colors, effects, and information',
      instruction: 'Customize your card! Try different colors and effects.',
    },
    {
      id: 4,
      title: 'Sharing Methods',
      description: 'Generate QR codes and sharing links',
      instruction: 'Learn how to share your card. Generate QR codes and direct links.',
    },
    {
      id: 5,
      title: 'Analytics Dashboard',
      description: 'Track scans and engagement',
      instruction: 'See how your networking performs with real-time analytics.',
    }
  ];

  const currentLevelData = levels.find(level => level.id === currentLevel);

  const completeLevel = (levelId) => {
    setCompletedLevels(prev => new Set([...prev, levelId]));
    if (levelId === currentLevel && levelId < levels.length) {
      setTimeout(() => setCurrentLevel(levelId + 1), 1000);
    }
  };

  const resetDemo = () => {
    setCurrentLevel(1);
    setCompletedLevels(new Set());
    setShowInstructions(true);
    setDemoData(prev => ({
      ...prev,
      scanCount: 0,
      shareCount: 0
    }));
  };

  // Simple AR Card Component
  const ARCard = ({ data, onInteraction }) => {
    const [isHovered, setIsHovered] = useState(false);
    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
    const cardRef = useRef(null);

    const handleMouseMove = (e) => {
      if (cardRef.current) {
        const rect = cardRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        setMousePosition({
          x: (e.clientX - centerX) / 15,
          y: (e.clientY - centerY) / 15
        });
      }
    };

    const theme = {
      cyan: '#00f5ff',
      purple: '#8b5cf6',
      green: '#10b981',
      orange: '#f59e0b'
    };

    const cardColor = theme[data.cardColor] || theme.cyan;

    return (
      <div className="flex items-center justify-center min-h-[400px] relative">
        <motion.div
          ref={cardRef}
          className="relative w-80 h-48 cursor-pointer"
          style={{
            transform: `perspective(1000px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg)`,
            transformStyle: 'preserve-3d'
          }}
          onMouseMove={handleMouseMove}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => {
            setIsHovered(false);
            setMousePosition({ x: 0, y: 0 });
          }}
          onClick={() => onInteraction?.()}
          whileHover={{ scale: 1.05 }}
          animate={{
            boxShadow: [`0 0 20px ${cardColor}40`, `0 0 30px ${cardColor}60`, `0 0 20px ${cardColor}40`]
          }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <div 
            className="absolute inset-0 rounded-xl shadow-2xl border-2 overflow-hidden"
            style={{
              background: `linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%)`,
              borderColor: cardColor,
              boxShadow: isHovered ? `0 0 30px ${cardColor}40` : '0 10px 30px rgba(0,0,0,0.3)'
            }}
          >
            {/* AR Corner Indicators */}
            <div className="absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 animate-pulse" style={{ borderColor: cardColor }} />
            <div className="absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 animate-pulse" style={{ borderColor: cardColor }} />
            <div className="absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 animate-pulse" style={{ borderColor: cardColor }} />
            <div className="absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 animate-pulse" style={{ borderColor: cardColor }} />

            {/* Card Content */}
            <div className="p-6 h-full flex flex-col justify-between relative z-10">
              <div className="flex items-start justify-between">
                <div>
                  <motion.div
                    className="w-12 h-12 rounded-full mb-3 flex items-center justify-center text-lg font-bold text-black"
                    style={{ background: cardColor }}
                    animate={{ rotate: isHovered ? 360 : 0 }}
                    transition={{ duration: 1 }}
                  >
                    {data.userName.split(' ').map(n => n[0]).join('')}
                  </motion.div>
                  
                  <h3 className="text-lg font-bold text-white mb-1">{data.userName}</h3>
                  <p className="text-gray-300 text-sm mb-1">{data.userTitle}</p>
                  <p className="text-gray-400 text-xs">{data.userCompany}</p>
                </div>

                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center text-xs font-bold"
                  style={{ background: cardColor + '20', color: cardColor }}
                >
                  AR
                </div>
              </div>

              <div className="space-y-1">
                <div className="flex items-center space-x-2 text-sm text-gray-300">
                  <span style={{ color: cardColor }}>📧</span>
                  <span className="text-xs">{data.userEmail}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-300">
                  <span style={{ color: cardColor }}>📱</span>
                  <span className="text-xs">{data.userPhone}</span>
                </div>
              </div>
            </div>

            {/* Holographic Effect */}
            {isHovered && (
              <motion.div
                className="absolute inset-0 rounded-xl pointer-events-none"
                style={{
                  background: `linear-gradient(45deg, transparent 30%, ${cardColor}10 50%, transparent 70%)`,
                }}
                animate={{ x: ['-100%', '100%'] }}
                transition={{ duration: 1.5, ease: "easeInOut" }}
              />
            )}
          </div>
        </motion.div>

        {/* Stats Display */}
        <div className="absolute top-4 right-4 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-3">
          <div className="text-xs text-text-secondary mb-1">Card Stats</div>
          <div className="text-sm text-text-primary">
            Scans: <span style={{ color: cardColor }}>{data.scanCount}</span>
          </div>
          <div className="text-sm text-text-primary">
            Shares: <span style={{ color: cardColor }}>{data.shareCount}</span>
          </div>
        </div>
      </div>
    );
  };

  // Simple Camera Simulator
  const CameraSimulator = ({ data, onScan }) => {
    const [isScanning, setIsScanning] = useState(false);
    const [showCard, setShowCard] = useState(false);

    const startScan = () => {
      setIsScanning(true);
      setTimeout(() => {
        setIsScanning(false);
        setShowCard(true);
        onScan?.();
      }, 2000);
    };

    return (
      <div className="space-y-6">
        <div className="text-center">
          <Button onClick={startScan} disabled={isScanning}>
            {isScanning ? '🔍 Scanning...' : '📷 Start AR Scan'}
          </Button>
        </div>

        <div className="relative w-full max-w-lg mx-auto aspect-video bg-black rounded-xl overflow-hidden border-2 border-border">
          {/* Simulated Camera Feed */}
          <div className="relative w-full h-full bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900">
            {isScanning && (
              <motion.div
                className="absolute left-0 right-0 h-1 bg-primary-cyan"
                animate={{ top: ['0%', '100%'] }}
                transition={{ duration: 2, ease: "linear" }}
              />
            )}

            {showCard && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
              >
                <div className="w-48 h-32 bg-surface border-2 border-primary-cyan rounded-lg p-3">
                  <div className="text-primary-cyan text-sm font-bold">{data.userName}</div>
                  <div className="text-text-secondary text-xs">{data.userTitle}</div>
                  <div className="text-text-muted text-xs">{data.userCompany}</div>
                </div>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderCurrentComponent = () => {
    switch (currentLevel) {
      case 1:
        return <ARCard data={demoData} onInteraction={() => completeLevel(1)} />;
      case 2:
        return (
          <CameraSimulator 
            data={demoData} 
            onScan={() => {
              setDemoData(prev => ({ ...prev, scanCount: prev.scanCount + 1 }));
              completeLevel(2);
            }}
          />
        );
      case 3:
        return (
          <div className="space-y-6">
            <ARCard data={demoData} />
            <div className="grid grid-cols-4 gap-2 max-w-xs mx-auto">
              {['cyan', 'purple', 'green', 'orange'].map(color => (
                <button
                  key={color}
                  onClick={() => {
                    setDemoData(prev => ({ ...prev, cardColor: color }));
                    completeLevel(3);
                  }}
                  className={`w-12 h-12 rounded-lg border-2 transition-all ${
                    demoData.cardColor === color ? 'border-white scale-110' : 'border-transparent'
                  }`}
                  style={{
                    background: color === 'cyan' ? '#00f5ff' : 
                              color === 'purple' ? '#8b5cf6' :
                              color === 'green' ? '#10b981' : '#f59e0b'
                  }}
                />
              ))}
            </div>
          </div>
        );
      case 4:
        return (
          <div className="text-center space-y-6">
            <div className="bg-white p-4 rounded-lg inline-block">
              <div className="w-32 h-32 grid grid-cols-8 gap-1">
                {[...Array(64)].map((_, i) => (
                  <div key={i} className={`aspect-square ${Math.random() > 0.5 ? 'bg-black' : 'bg-white'}`} />
                ))}
              </div>
            </div>
            <Button onClick={() => {
              setDemoData(prev => ({ ...prev, shareCount: prev.shareCount + 1 }));
              completeLevel(4);
            }}>
              📤 Share Card
            </Button>
          </div>
        );
      case 5:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-surface border border-border rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-primary-cyan">{1247 + demoData.scanCount}</div>
                <div className="text-sm text-text-secondary">Total Scans</div>
              </div>
              <div className="bg-surface border border-border rounded-lg p-4 text-center">
                <div className="text-2xl font-bold text-accent-green">{892 + demoData.shareCount}</div>
                <div className="text-sm text-text-secondary">Unique Views</div>
              </div>
            </div>
            <Button onClick={() => completeLevel(5)}>
              📊 View Full Analytics
            </Button>
          </div>
        );
      default:
        return <div>Loading...</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-surface to-background py-12">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-hero gradient-text mb-6">
            Interactive AR Demo
          </h1>
          <p className="text-xl text-text-secondary mb-8 max-w-3xl mx-auto">
            Experience the complete NameCardAI journey. Go through each level to see how AR business cards revolutionize networking.
          </p>
          
          {/* Progress */}
          <div className="max-w-md mx-auto mb-8">
            <div className="flex justify-between text-sm text-text-secondary mb-2">
              <span>Progress</span>
              <span>{completedLevels.size}/{levels.length} Complete</span>
            </div>
            <div className="w-full bg-border rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(completedLevels.size / levels.length) * 100}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
          </div>
        </motion.div>

        <div className="grid lg:grid-cols-4 gap-8">
          {/* Level Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 sticky top-24">
              <h3 className="text-lg font-bold text-text-primary mb-4">Demo Levels</h3>
              <div className="space-y-3">
                {levels.map((level) => (
                  <motion.div
                    key={level.id}
                    whileHover={{ scale: 1.02 }}
                    className={`p-3 rounded-lg border cursor-pointer transition-all ${
                      currentLevel === level.id
                        ? 'bg-primary-cyan/10 border-primary-cyan shadow-lg shadow-primary-cyan/25'
                        : completedLevels.has(level.id)
                        ? 'bg-accent-green/10 border-accent-green'
                        : 'bg-surface border-border hover:border-primary-cyan/50'
                    }`}
                    onClick={() => setCurrentLevel(level.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        completedLevels.has(level.id)
                          ? 'bg-accent-green text-black'
                          : currentLevel === level.id
                          ? 'bg-primary-cyan text-black'
                          : 'bg-border text-text-secondary'
                      }`}>
                        {completedLevels.has(level.id) ? '✓' : level.id}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-text-primary text-sm">{level.title}</h4>
                        <p className="text-xs text-text-secondary">{level.description}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
              
              <div className="mt-6 pt-6 border-t border-border">
                <Button variant="outline" size="sm" onClick={resetDemo} className="w-full">
                  🔄 Reset Demo
                </Button>
              </div>
            </div>
          </div>

          {/* Demo Area */}
          <div className="lg:col-span-3">
            <motion.div
              key={currentLevel}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-8 min-h-[600px] relative"
            >
              {/* Level Header */}
              <div className="mb-8">
                <div className="flex items-center space-x-3 mb-4">
                  <div className="w-10 h-10 bg-primary-cyan text-black rounded-full flex items-center justify-center font-bold">
                    {currentLevel}
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-text-primary">
                      {currentLevelData?.title}
                    </h2>
                    <p className="text-text-secondary">
                      {currentLevelData?.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Demo Component */}
              <div className="relative">
                {renderCurrentComponent()}
              </div>

              {/* Instructions Overlay */}
              <AnimatePresence>
                {showInstructions && currentLevelData && (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
                  >
                    <motion.div
                      initial={{ scale: 0.8, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.8, opacity: 0 }}
                      className="bg-surface border border-border rounded-xl p-8 max-w-md mx-4 text-center"
                    >
                      <div className="text-4xl mb-4">💡</div>
                      <h3 className="text-xl font-bold text-text-primary mb-4">Instructions</h3>
                      <p className="text-text-secondary mb-6">{currentLevelData.instruction}</p>
                      <div className="flex space-x-3 justify-center">
                        <Button onClick={() => setShowInstructions(false)}>Got it!</Button>
                        <Button variant="ghost" onClick={() => setShowInstructions(false)}>Skip Tutorial</Button>
                      </div>
                    </motion.div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
        </div>

        {/* Completion Message */}
        <AnimatePresence>
          {completedLevels.size === levels.length && (
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
              className="mt-12 text-center bg-gradient-to-r from-accent-green/10 to-primary-cyan/10 border border-accent-green/30 rounded-xl p-8"
            >
              <div className="text-6xl mb-4">🎉</div>
              <h3 className="text-2xl font-bold text-text-primary mb-4">
                Demo Complete!
              </h3>
              <p className="text-text-secondary mb-6 max-w-2xl mx-auto">
                Congratulations! You've experienced the full NameCardAI journey. Ready to create your own AR business card?
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" href="/signup">
                  ✨ Start Your AR Journey
                </Button>
                <Button variant="outline" size="lg" onClick={resetDemo}>
                  🔄 Try Demo Again
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
