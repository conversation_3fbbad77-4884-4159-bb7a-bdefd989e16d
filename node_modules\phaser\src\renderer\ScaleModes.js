/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * Phaser Scale Modes.
 *
 * @namespace Phaser.ScaleModes
 * @since 3.0.0
 */

var ScaleModes = {

    /**
     * Default Scale Mode (Linear).
     *
     * @name Phaser.ScaleModes.DEFAULT
     * @type {number}
     * @readonly
     * @since 3.0.0
     */
    DEFAULT: 0,

    /**
     * Linear Scale Mode.
     *
     * @name Phaser.ScaleModes.LINEAR
     * @type {number}
     * @readonly
     * @since 3.0.0
     */
    LINEAR: 0,

    /**
     * Nearest Scale Mode.
     *
     * @name Phaser.ScaleModes.NEAREST
     * @type {number}
     * @readonly
     * @since 3.0.0
     */
    NEAREST: 1

};

module.exports = ScaleModes;
