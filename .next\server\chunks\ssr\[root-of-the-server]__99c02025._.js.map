{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,OAAO,kBACX,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;wBAAmC;sCACvC,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMpD,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;;;;kDACD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAKP,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;AAIU,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,8OAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,8OAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,8OAAC;;sEACC,8OAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,8OAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;4CAAG,WAAU;;gDAAkC;8DACtC,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { motion, useAnimation, useInView } from 'framer-motion';\nimport Button from '../ui/Button';\n\n// Typing Effect Component\nconst TypingText = ({ text, delay = 0, speed = 50 }) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [showCursor, setShowCursor] = useState(true);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (currentIndex < text.length) {\n        setDisplayText(text.slice(0, currentIndex + 1));\n        setCurrentIndex(currentIndex + 1);\n      }\n    }, delay + currentIndex * speed);\n\n    return () => clearTimeout(timer);\n  }, [currentIndex, text, delay, speed]);\n\n  useEffect(() => {\n    const cursorTimer = setInterval(() => {\n      setShowCursor(prev => !prev);\n    }, 500);\n\n    return () => clearInterval(cursorTimer);\n  }, []);\n\n  return (\n    <span>\n      {displayText}\n      <span className={`${showCursor ? 'opacity-100' : 'opacity-0'} transition-opacity`}>|</span>\n    </span>\n  );\n};\n\n// 3D Card Preview Component\nconst ARCardPreview = () => {\n  const cardRef = useRef(null);\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n\n  useEffect(() => {\n    const handleMouseMove = (e) => {\n      if (cardRef.current) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n        \n        setMousePosition({\n          x: (e.clientX - centerX) / 10,\n          y: (e.clientY - centerY) / 10\n        });\n      }\n    };\n\n    window.addEventListener('mousemove', handleMouseMove);\n    return () => window.removeEventListener('mousemove', handleMouseMove);\n  }, []);\n\n  return (\n    <motion.div\n      ref={cardRef}\n      className=\"relative w-80 h-48 mx-auto\"\n      style={{\n        transform: `perspective(1000px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg)`,\n        transformStyle: 'preserve-3d'\n      }}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 1, delay: 0.5 }}\n    >\n      {/* Main Card */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-surface to-border rounded-xl shadow-2xl border border-primary-cyan/30\">\n        {/* AR Corner Indicators */}\n        <div className=\"absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-primary-cyan animate-pulse\" />\n        <div className=\"absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-primary-cyan animate-pulse\" />\n        <div className=\"absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-primary-cyan animate-pulse\" />\n        <div className=\"absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-primary-cyan animate-pulse\" />\n        \n        {/* Card Content */}\n        <div className=\"p-6 h-full flex flex-col justify-between\">\n          <div>\n            <div className=\"w-12 h-12 bg-gradient-to-br from-primary-cyan to-primary-purple rounded-full mb-3 animate-pulse\" />\n            <h3 className=\"text-lg font-bold text-text-primary mb-1\">John Doe</h3>\n            <p className=\"text-sm text-text-secondary\">Senior Developer</p>\n            <p className=\"text-xs text-text-muted\">TechCorp Inc.</p>\n          </div>\n          \n          <div className=\"flex space-x-2\">\n            <div className=\"w-6 h-6 bg-primary-cyan/20 rounded border border-primary-cyan animate-pulse\" />\n            <div className=\"w-6 h-6 bg-primary-purple/20 rounded border border-primary-purple animate-pulse\" />\n            <div className=\"w-6 h-6 bg-accent-green/20 rounded border border-accent-green animate-pulse\" />\n          </div>\n        </div>\n        \n        {/* Holographic Effect */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-primary-cyan/10 to-transparent animate-pulse rounded-xl\" />\n      </div>\n      \n      {/* Floating Particles */}\n      {[...Array(6)].map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-1 h-1 bg-primary-cyan rounded-full\"\n          style={{\n            left: `${20 + i * 15}%`,\n            top: `${30 + (i % 2) * 40}%`,\n          }}\n          animate={{\n            y: [-10, 10, -10],\n            opacity: [0.3, 1, 0.3],\n          }}\n          transition={{\n            duration: 2 + i * 0.5,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n        />\n      ))}\n    </motion.div>\n  );\n};\n\nexport default function HeroSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true });\n  const controls = useAnimation();\n\n  useEffect(() => {\n    if (isInView) {\n      controls.start('visible');\n    }\n  }, [isInView, controls]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.8, ease: \"easeOut\" }\n    }\n  };\n\n  return (\n    <section \n      ref={ref}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-surface to-background\"\n    >\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        {/* Animated Grid */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"grid grid-cols-12 grid-rows-8 h-full w-full\">\n            {[...Array(96)].map((_, i) => (\n              <motion.div\n                key={i}\n                className=\"border border-primary-cyan/10\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: [0, 0.3, 0] }}\n                transition={{\n                  duration: 3,\n                  delay: i * 0.05,\n                  repeat: Infinity,\n                  repeatDelay: 2\n                }}\n              />\n            ))}\n          </div>\n        </div>\n        \n        {/* Floating Orbs */}\n        {[...Array(8)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-primary-cyan/30 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              x: [0, 100, 0],\n              y: [0, -100, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: 8 + i * 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={controls}\n          className=\"grid lg:grid-cols-2 gap-12 items-center\"\n        >\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <motion.div variants={itemVariants} className=\"mb-6\">\n              <span className=\"inline-block px-4 py-2 bg-primary-cyan/10 border border-primary-cyan/30 rounded-full text-primary-cyan text-sm font-medium mb-4\">\n                🚀 The Future of Networking is Here\n              </span>\n            </motion.div>\n\n            <motion.h1 variants={itemVariants} className=\"text-hero mb-6\">\n              <span className=\"gradient-text\">Your Name.</span>\n              <br />\n              <TypingText text=\"Reinvented.\" delay={1000} speed={100} />\n            </motion.h1>\n\n            <motion.p variants={itemVariants} className=\"text-xl text-text-secondary mb-8 max-w-2xl\">\n              AR-enhanced digital business cards that let you share stunning, interactive profiles via QR, NFC, facial recognition, or camera scan—\n              <span className=\"text-primary-cyan font-semibold\">no app required</span>.\n            </motion.p>\n\n            <motion.div variants={itemVariants} className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <Button \n                size=\"lg\" \n                href=\"/demo\"\n                className=\"group\"\n              >\n                <span className=\"mr-2\">🎮</span>\n                Try Live Demo\n                <motion.span\n                  className=\"ml-2\"\n                  animate={{ x: [0, 5, 0] }}\n                  transition={{ duration: 1.5, repeat: Infinity }}\n                >\n                  →\n                </motion.span>\n              </Button>\n              \n              <Button \n                variant=\"outline\" \n                size=\"lg\"\n                href=\"/signup\"\n              >\n                <span className=\"mr-2\">✨</span>\n                Get Started Free\n              </Button>\n            </motion.div>\n\n            <motion.div variants={itemVariants} className=\"mt-8 flex items-center justify-center lg:justify-start space-x-6 text-sm text-text-muted\">\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n                No App Required\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n                Works Everywhere\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n                AR-Powered\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Right Content - AR Card Preview */}\n          <motion.div variants={itemVariants} className=\"relative\">\n            <ARCardPreview />\n            \n            {/* Stats Overlay */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 1.5 }}\n              className=\"absolute -bottom-6 -left-6 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg\"\n            >\n              <div className=\"text-2xl font-bold text-primary-cyan\">70%</div>\n              <div className=\"text-xs text-text-secondary\">Higher Retention</div>\n            </motion.div>\n            \n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 1.8 }}\n              className=\"absolute -top-6 -right-6 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg\"\n            >\n              <div className=\"text-2xl font-bold text-accent-green\">7B+</div>\n              <div className=\"text-xs text-text-secondary\">Cards Replaced</div>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        animate={{ y: [0, 10, 0] }}\n        transition={{ duration: 2, repeat: Infinity }}\n      >\n        <div className=\"w-6 h-10 border-2 border-primary-cyan rounded-full flex justify-center\">\n          <motion.div\n            className=\"w-1 h-3 bg-primary-cyan rounded-full mt-2\"\n            animate={{ opacity: [1, 0, 1] }}\n            transition={{ duration: 2, repeat: Infinity }}\n          />\n        </div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,0BAA0B;AAC1B,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,IAAI,eAAe,KAAK,MAAM,EAAE;gBAC9B,eAAe,KAAK,KAAK,CAAC,GAAG,eAAe;gBAC5C,gBAAgB,eAAe;YACjC;QACF,GAAG,QAAQ,eAAe;QAE1B,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;QAAM;QAAO;KAAM;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,YAAY;YAC9B,cAAc,CAAA,OAAQ,CAAC;QACzB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC;;YACE;0BACD,8OAAC;gBAAK,WAAW,GAAG,aAAa,gBAAgB,YAAY,mBAAmB,CAAC;0BAAE;;;;;;;;;;;;AAGzF;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB;IACpB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;gBAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;gBAEzC,iBAAiB;oBACf,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;oBAC3B,GAAG,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;gBAC7B;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,aAAa;QACrC,OAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;IACvD,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAU;QACV,OAAO;YACL,WAAW,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC;YAC9F,gBAAgB;QAClB;QACA,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAG,OAAO;QAAI;;0BAGtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDAA8B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;0CAGzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAIhB;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;wBACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;oBAC9B;oBACA,SAAS;wBACP,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;wBACjB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU,IAAI,IAAI;wBAClB,QAAQ;wBACR,MAAM;oBACR;mBAdK;;;;;;;;;;;AAmBf;AAEe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;IAAK;IAC7C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC;QACjB;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;4CAAC;4CAAG;4CAAK;yCAAE;oCAAC;oCAChC,YAAY;wCACV,UAAU;wCACV,OAAO,IAAI;wCACX,QAAQ;wCACR,aAAa;oCACf;mCATK;;;;;;;;;;;;;;;oBAgBZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAChC;4BACA,SAAS;gCACP,GAAG;oCAAC;oCAAG;oCAAK;iCAAE;gCACd,GAAG;oCAAC;oCAAG,CAAC;oCAAK;iCAAE;gCACf,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;4BACpB;4BACA,YAAY;gCACV,UAAU,IAAI,IAAI;gCAClB,QAAQ;gCACR,MAAM;4BACR;2BAfK;;;;;;;;;;;0BAoBX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAS;oBACT,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;8CAC5C,cAAA,8OAAC;wCAAK,WAAU;kDAAkI;;;;;;;;;;;8CAKpJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCAAC,UAAU;oCAAc,WAAU;;sDAC3C,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,8OAAC;;;;;sDACD,8OAAC;4CAAW,MAAK;4CAAc,OAAO;4CAAM,OAAO;;;;;;;;;;;;8CAGrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAAC,UAAU;oCAAc,WAAU;;wCAA6C;sDAEvF,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;wCAAsB;;;;;;;8CAG1E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC,iIAAA,CAAA,UAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;8DAEhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,WAAU;oDACV,SAAS;wDAAE,GAAG;4DAAC;4DAAG;4DAAG;yDAAE;oDAAC;oDACxB,YAAY;wDAAE,UAAU;wDAAK,QAAQ;oDAAS;8DAC/C;;;;;;;;;;;;sDAKH,8OAAC,iIAAA,CAAA,UAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,MAAK;;8DAEL,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAQ;;;;;;;;;;;;;8CAKnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;oCAAc,WAAU;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;gDAA4D;;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;gDAA4D;;;;;;;sDAG9E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;gDAA4D;;;;;;;;;;;;;;;;;;;sCAOlF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAG/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;wBAAC;wBAAG;wBAAI;qBAAE;gBAAC;gBACzB,YAAY;oBAAE,UAAU;oBAAG,QAAQ;gBAAS;0BAE5C,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;gCAAC;gCAAG;gCAAG;6BAAE;wBAAC;wBAC9B,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;;;;;;;;;;;;;;;;;;;;;;AAMxD", "debugId": null}}, {"offset": {"line": 2021, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { motion, useAnimation, useInView } from 'framer-motion';\n\nconst StatCard = ({ number, label, delay = 0 }) => (\n  <motion.div\n    initial={{ opacity: 0, scale: 0.8 }}\n    whileInView={{ opacity: 1, scale: 1 }}\n    transition={{ duration: 0.6, delay }}\n    className=\"text-center p-6 bg-surface/50 backdrop-blur-sm border border-border rounded-lg hover:border-primary-cyan/50 transition-all duration-300\"\n  >\n    <div className=\"text-3xl font-bold gradient-text mb-2\">{number}</div>\n    <div className=\"text-text-secondary text-sm\">{label}</div>\n  </motion.div>\n);\n\nconst ProblemCard = ({ icon, title, description, delay = 0 }) => (\n  <motion.div\n    initial={{ opacity: 0, x: -50 }}\n    whileInView={{ opacity: 1, x: 0 }}\n    transition={{ duration: 0.6, delay }}\n    className=\"flex items-start space-x-4 p-6 bg-red-900/10 border border-red-500/20 rounded-lg hover:border-red-500/40 transition-all duration-300\"\n  >\n    <div className=\"flex-shrink-0 w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center text-red-400 text-xl\">\n      {icon}\n    </div>\n    <div>\n      <h3 className=\"text-lg font-semibold text-text-primary mb-2\">{title}</h3>\n      <p className=\"text-text-secondary\">{description}</p>\n    </div>\n  </motion.div>\n);\n\nconst SolutionCard = ({ icon, title, description, delay = 0 }) => (\n  <motion.div\n    initial={{ opacity: 0, x: 50 }}\n    whileInView={{ opacity: 1, x: 0 }}\n    transition={{ duration: 0.6, delay }}\n    className=\"flex items-start space-x-4 p-6 bg-accent-green/10 border border-accent-green/20 rounded-lg hover:border-accent-green/40 transition-all duration-300\"\n  >\n    <div className=\"flex-shrink-0 w-12 h-12 bg-accent-green/20 rounded-lg flex items-center justify-center text-accent-green text-xl\">\n      {icon}\n    </div>\n    <div>\n      <h3 className=\"text-lg font-semibold text-text-primary mb-2\">{title}</h3>\n      <p className=\"text-text-secondary\">{description}</p>\n    </div>\n  </motion.div>\n);\n\nexport default function ProblemSolutionSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const controls = useAnimation();\n\n  useEffect(() => {\n    if (isInView) {\n      controls.start('visible');\n    }\n  }, [isInView, controls]);\n\n  const problems = [\n    {\n      icon: \"🗑️\",\n      title: \"Massive Waste\",\n      description: \"7 billion+ paper cards printed yearly with 88% thrown away within a week\"\n    },\n    {\n      icon: \"📱\",\n      title: \"Tech Limitations\",\n      description: \"QR/NFC cards require specific apps, compatible devices, and battery life\"\n    },\n    {\n      icon: \"😴\",\n      title: \"Forgettable\",\n      description: \"Traditional cards lack visual impact and memorable experiences\"\n    },\n    {\n      icon: \"🔄\",\n      title: \"Manual Effort\",\n      description: \"Physical cards require manual data entry and offer no digital engagement\"\n    }\n  ];\n\n  const solutions = [\n    {\n      icon: \"🌐\",\n      title: \"Universal Access\",\n      description: \"Works in any browser without app downloads or special hardware\"\n    },\n    {\n      icon: \"🎯\",\n      title: \"Multiple Methods\",\n      description: \"Share via QR, NFC, camera scan, or just remembering a name\"\n    },\n    {\n      icon: \"✨\",\n      title: \"Memorable AR\",\n      description: \"3D animations and immersive experiences boost retention by 70%\"\n    },\n    {\n      icon: \"🌱\",\n      title: \"Eco-Friendly\",\n      description: \"Digital-first approach eliminates paper waste and environmental impact\"\n    }\n  ];\n\n  const stats = [\n    { number: \"7B+\", label: \"Cards Printed Yearly\" },\n    { number: \"88%\", label: \"Thrown Away\" },\n    { number: \"70%\", label: \"Higher Retention\" },\n    { number: \"0\", label: \"Apps Required\" }\n  ];\n\n  return (\n    <section ref={ref} className=\"py-20 bg-gradient-to-b from-background to-surface relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        {/* Animated Lines */}\n        {[...Array(5)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute h-px bg-gradient-to-r from-transparent via-primary-cyan/30 to-transparent\"\n            style={{\n              top: `${20 + i * 20}%`,\n              left: 0,\n              right: 0,\n            }}\n            animate={{\n              opacity: [0, 0.5, 0],\n              scaleX: [0, 1, 0],\n            }}\n            transition={{\n              duration: 4,\n              delay: i * 0.5,\n              repeat: Infinity,\n              repeatDelay: 2\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-section gradient-text mb-6\">\n            The Problem with Traditional Networking\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            Paper business cards are outdated, wasteful, and forgettable. \n            Current digital solutions still fall short of creating meaningful connections.\n          </p>\n        </motion.div>\n\n        {/* Statistics */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mb-20\"\n        >\n          {stats.map((stat, index) => (\n            <StatCard key={stat.label} {...stat} delay={index * 0.1} />\n          ))}\n        </motion.div>\n\n        {/* Problem vs Solution */}\n        <div className=\"grid lg:grid-cols-2 gap-12 items-start\">\n          {/* Problems */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-8\"\n            >\n              <h3 className=\"text-2xl font-bold text-red-400 mb-4 flex items-center\">\n                <span className=\"mr-3\">❌</span>\n                Current Problems\n              </h3>\n              <p className=\"text-text-secondary\">\n                Traditional networking methods are broken and need a complete reimagining.\n              </p>\n            </motion.div>\n            \n            <div className=\"space-y-6\">\n              {problems.map((problem, index) => (\n                <ProblemCard key={problem.title} {...problem} delay={index * 0.1} />\n              ))}\n            </div>\n          </div>\n\n          {/* Solutions */}\n          <div>\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-8\"\n            >\n              <h3 className=\"text-2xl font-bold text-accent-green mb-4 flex items-center\">\n                <span className=\"mr-3\">✅</span>\n                Our Solutions\n              </h3>\n              <p className=\"text-text-secondary\">\n                NameCardAI revolutionizes networking with AR-powered digital business cards.\n              </p>\n            </motion.div>\n            \n            <div className=\"space-y-6\">\n              {solutions.map((solution, index) => (\n                <SolutionCard key={solution.title} {...solution} delay={index * 0.1} />\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Visual Comparison */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mt-20 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-text-primary mb-8\">\n            The Transformation\n          </h3>\n          \n          <div className=\"grid md:grid-cols-3 gap-8 items-center\">\n            {/* Before */}\n            <div className=\"p-8 bg-red-900/10 border border-red-500/20 rounded-lg\">\n              <div className=\"text-6xl mb-4\">📄</div>\n              <h4 className=\"text-lg font-semibold text-red-400 mb-2\">Before</h4>\n              <p className=\"text-text-secondary text-sm\">\n                Paper cards that get lost, damaged, or thrown away\n              </p>\n            </div>\n\n            {/* Arrow */}\n            <div className=\"hidden md:block\">\n              <motion.div\n                animate={{ x: [0, 10, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n                className=\"text-4xl text-primary-cyan\"\n              >\n                →\n              </motion.div>\n            </div>\n\n            {/* After */}\n            <div className=\"p-8 bg-accent-green/10 border border-accent-green/20 rounded-lg\">\n              <div className=\"text-6xl mb-4\">✨</div>\n              <h4 className=\"text-lg font-semibold text-accent-green mb-2\">After</h4>\n              <p className=\"text-text-secondary text-sm\">\n                AR-enhanced digital cards that create lasting impressions\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,WAAW,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE,iBAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,aAAa;YAAE,SAAS;YAAG,OAAO;QAAE;QACpC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BAAyC;;;;;;0BACxD,8OAAC;gBAAI,WAAU;0BAA+B;;;;;;;;;;;;AAIlD,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,iBAC1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAC9D,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;;;;;;;AAK1C,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,iBAC3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;kCAC9D,8OAAC;wBAAE,WAAU;kCAAuB;;;;;;;;;;;;;;;;;;AAK3B,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC;QACjB;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,QAAQ;YAAO,OAAO;QAAuB;QAC/C;YAAE,QAAQ;YAAO,OAAO;QAAc;QACtC;YAAE,QAAQ;YAAO,OAAO;QAAmB;QAC3C;YAAE,QAAQ;YAAK,OAAO;QAAgB;KACvC;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;0BAEZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;4BACtB,MAAM;4BACN,OAAO;wBACT;wBACA,SAAS;4BACP,SAAS;gCAAC;gCAAG;gCAAK;6BAAE;4BACpB,QAAQ;gCAAC;gCAAG;gCAAG;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,OAAO,IAAI;4BACX,QAAQ;4BACR,aAAa;wBACf;uBAhBK;;;;;;;;;;0BAqBX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAGhD,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAO/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAA2B,GAAG,IAAI;gCAAE,OAAO,QAAQ;+BAArC,KAAK,KAAK;;;;;;;;;;kCAK7B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAQ;;;;;;;0DAGjC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gDAAiC,GAAG,OAAO;gDAAE,OAAO,QAAQ;+CAA3C,QAAQ,KAAK;;;;;;;;;;;;;;;;0CAMrC,8OAAC;;kDACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAO;;;;;;oDAAQ;;;;;;;0DAGjC,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;kDACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;gDAAmC,GAAG,QAAQ;gDAAE,OAAO,QAAQ;+CAA7C,SAAS,KAAK;;;;;;;;;;;;;;;;;;;;;;kCAOzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAM7C,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAG;oDAAI;iDAAE;4CAAC;4CACzB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;4CAC5C,WAAU;sDACX;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAC7D,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 2658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/sections/FeaturesSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { motion, useAnimation, useInView } from 'framer-motion';\n\nconst FeatureCard = ({ icon, title, description, delay = 0, isActive = false, onClick }) => (\n  <motion.div\n    initial={{ opacity: 0, y: 50 }}\n    whileInView={{ opacity: 1, y: 0 }}\n    transition={{ duration: 0.6, delay }}\n    whileHover={{ scale: 1.02 }}\n    className={`p-6 rounded-lg border cursor-pointer transition-all duration-300 ${\n      isActive \n        ? 'bg-primary-cyan/10 border-primary-cyan shadow-lg shadow-primary-cyan/25' \n        : 'bg-surface border-border hover:border-primary-cyan/50'\n    }`}\n    onClick={onClick}\n  >\n    <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-2xl mb-4 ${\n      isActive ? 'bg-primary-cyan/20' : 'bg-border'\n    }`}>\n      {icon}\n    </div>\n    <h3 className=\"text-lg font-semibold text-text-primary mb-2\">{title}</h3>\n    <p className=\"text-text-secondary text-sm\">{description}</p>\n  </motion.div>\n);\n\nconst FeatureDemo = ({ activeFeature }) => {\n  const demoContent = {\n    ar: {\n      title: \"AR Business Cards\",\n      description: \"3D animated cards that appear in real-world environments\",\n      visual: (\n        <div className=\"relative w-full h-64 bg-gradient-to-br from-surface to-border rounded-lg overflow-hidden\">\n          {/* AR Card Simulation */}\n          <div className=\"absolute inset-4 bg-gradient-to-br from-primary-cyan/20 to-primary-purple/20 rounded-lg border border-primary-cyan/50\">\n            {/* AR Corners */}\n            <div className=\"absolute top-2 left-2 w-4 h-4 border-l-2 border-t-2 border-primary-cyan animate-pulse\" />\n            <div className=\"absolute top-2 right-2 w-4 h-4 border-r-2 border-t-2 border-primary-cyan animate-pulse\" />\n            <div className=\"absolute bottom-2 left-2 w-4 h-4 border-l-2 border-b-2 border-primary-cyan animate-pulse\" />\n            <div className=\"absolute bottom-2 right-2 w-4 h-4 border-r-2 border-b-2 border-primary-cyan animate-pulse\" />\n            \n            {/* Floating Card */}\n            <motion.div\n              animate={{ y: [-5, 5, -5], rotateY: [0, 5, 0] }}\n              transition={{ duration: 3, repeat: Infinity }}\n              className=\"absolute inset-6 bg-gradient-to-br from-surface to-border rounded-lg shadow-lg border border-primary-cyan/30 p-4\"\n            >\n              <div className=\"w-8 h-8 bg-primary-cyan rounded-full mb-2\" />\n              <div className=\"h-2 bg-text-primary/20 rounded mb-1\" />\n              <div className=\"h-2 bg-text-primary/10 rounded w-3/4\" />\n            </motion.div>\n          </div>\n          \n          {/* Particles */}\n          {[...Array(8)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-1 h-1 bg-primary-cyan rounded-full\"\n              style={{\n                left: `${20 + i * 10}%`,\n                top: `${30 + (i % 2) * 40}%`,\n              }}\n              animate={{\n                opacity: [0, 1, 0],\n                scale: [0, 1, 0],\n              }}\n              transition={{\n                duration: 2,\n                delay: i * 0.2,\n                repeat: Infinity,\n              }}\n            />\n          ))}\n        </div>\n      )\n    },\n    sharing: {\n      title: \"Multiple Sharing Methods\",\n      description: \"QR codes, NFC, camera scan, or name lookup\",\n      visual: (\n        <div className=\"grid grid-cols-2 gap-4 h-64\">\n          {/* QR Code */}\n          <div className=\"bg-surface rounded-lg p-4 flex flex-col items-center justify-center border border-border\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-primary-cyan to-primary-purple rounded-lg mb-2 grid grid-cols-4 gap-1 p-2\">\n              {[...Array(16)].map((_, i) => (\n                <div key={i} className={`rounded-sm ${Math.random() > 0.5 ? 'bg-black' : 'bg-white'}`} />\n              ))}\n            </div>\n            <span className=\"text-xs text-text-secondary\">QR Code</span>\n          </div>\n          \n          {/* NFC */}\n          <div className=\"bg-surface rounded-lg p-4 flex flex-col items-center justify-center border border-border\">\n            <motion.div\n              animate={{ scale: [1, 1.1, 1] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-16 h-16 bg-accent-green/20 rounded-full flex items-center justify-center mb-2\"\n            >\n              <span className=\"text-2xl\">📱</span>\n            </motion.div>\n            <span className=\"text-xs text-text-secondary\">NFC Tap</span>\n          </div>\n          \n          {/* Camera */}\n          <div className=\"bg-surface rounded-lg p-4 flex flex-col items-center justify-center border border-border\">\n            <div className=\"w-16 h-16 bg-accent-orange/20 rounded-lg flex items-center justify-center mb-2\">\n              <span className=\"text-2xl\">📷</span>\n            </div>\n            <span className=\"text-xs text-text-secondary\">Camera Scan</span>\n          </div>\n          \n          {/* Name Search */}\n          <div className=\"bg-surface rounded-lg p-4 flex flex-col items-center justify-center border border-border\">\n            <div className=\"w-16 h-16 bg-accent-pink/20 rounded-lg flex items-center justify-center mb-2\">\n              <span className=\"text-2xl\">🔍</span>\n            </div>\n            <span className=\"text-xs text-text-secondary\">Name Lookup</span>\n          </div>\n        </div>\n      )\n    },\n    customization: {\n      title: \"Full Customization\",\n      description: \"3D avatars, custom animations, and branded experiences\",\n      visual: (\n        <div className=\"h-64 bg-surface rounded-lg p-4 border border-border\">\n          <div className=\"grid grid-cols-3 gap-4 h-full\">\n            {/* Avatar Options */}\n            <div className=\"space-y-2\">\n              <h4 className=\"text-xs font-semibold text-text-primary\">Avatars</h4>\n              {[...Array(4)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  whileHover={{ scale: 1.1 }}\n                  className=\"w-8 h-8 rounded-full bg-gradient-to-br from-primary-cyan to-primary-purple cursor-pointer\"\n                  style={{\n                    background: `linear-gradient(135deg, hsl(${i * 60}, 70%, 60%), hsl(${i * 60 + 60}, 70%, 60%))`\n                  }}\n                />\n              ))}\n            </div>\n            \n            {/* Animation Options */}\n            <div className=\"space-y-2\">\n              <h4 className=\"text-xs font-semibold text-text-primary\">Effects</h4>\n              {['Glow', 'Float', 'Spin', 'Pulse'].map((effect, i) => (\n                <div key={effect} className=\"text-xs text-text-secondary bg-border rounded px-2 py-1\">\n                  {effect}\n                </div>\n              ))}\n            </div>\n            \n            {/* Color Themes */}\n            <div className=\"space-y-2\">\n              <h4 className=\"text-xs font-semibold text-text-primary\">Themes</h4>\n              {[\n                'linear-gradient(135deg, #00f5ff, #8b5cf6)',\n                'linear-gradient(135deg, #10b981, #f59e0b)',\n                'linear-gradient(135deg, #ec4899, #3b82f6)',\n                'linear-gradient(135deg, #f59e0b, #ec4899)'\n              ].map((gradient, i) => (\n                <motion.div\n                  key={i}\n                  whileHover={{ scale: 1.1 }}\n                  className=\"w-8 h-4 rounded cursor-pointer\"\n                  style={{ background: gradient }}\n                />\n              ))}\n            </div>\n          </div>\n        </div>\n      )\n    },\n    analytics: {\n      title: \"Smart Analytics\",\n      description: \"Track engagement, scan rates, and networking ROI\",\n      visual: (\n        <div className=\"h-64 bg-surface rounded-lg p-4 border border-border\">\n          <div className=\"grid grid-cols-2 gap-4 h-full\">\n            {/* Chart */}\n            <div className=\"space-y-2\">\n              <h4 className=\"text-xs font-semibold text-text-primary\">Scan Activity</h4>\n              <div className=\"flex items-end space-x-1 h-32\">\n                {[40, 65, 30, 80, 55, 90, 70].map((height, i) => (\n                  <motion.div\n                    key={i}\n                    initial={{ height: 0 }}\n                    animate={{ height: `${height}%` }}\n                    transition={{ duration: 1, delay: i * 0.1 }}\n                    className=\"bg-primary-cyan/60 rounded-t flex-1\"\n                  />\n                ))}\n              </div>\n            </div>\n            \n            {/* Stats */}\n            <div className=\"space-y-3\">\n              <h4 className=\"text-xs font-semibold text-text-primary\">Metrics</h4>\n              {[\n                { label: 'Total Scans', value: '1,247' },\n                { label: 'Unique Views', value: '892' },\n                { label: 'Conversion Rate', value: '23%' },\n                { label: 'Avg. Time', value: '45s' }\n              ].map((stat, i) => (\n                <div key={stat.label} className=\"flex justify-between text-xs\">\n                  <span className=\"text-text-secondary\">{stat.label}</span>\n                  <span className=\"text-primary-cyan font-semibold\">{stat.value}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      )\n    }\n  };\n\n  return (\n    <motion.div\n      key={activeFeature}\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5 }}\n      className=\"lg:col-span-2\"\n    >\n      <div className=\"mb-6\">\n        <h3 className=\"text-2xl font-bold text-text-primary mb-2\">\n          {demoContent[activeFeature].title}\n        </h3>\n        <p className=\"text-text-secondary\">\n          {demoContent[activeFeature].description}\n        </p>\n      </div>\n      {demoContent[activeFeature].visual}\n    </motion.div>\n  );\n};\n\nexport default function FeaturesSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const [activeFeature, setActiveFeature] = useState('ar');\n\n  const features = [\n    {\n      id: 'ar',\n      icon: '🥽',\n      title: 'AR Business Cards',\n      description: '3D animated cards that appear in real-world environments through your camera'\n    },\n    {\n      id: 'sharing',\n      icon: '📤',\n      title: 'Universal Sharing',\n      description: 'Multiple ways to share: QR codes, NFC, camera scan, or name lookup'\n    },\n    {\n      id: 'customization',\n      icon: '🎨',\n      title: 'Full Customization',\n      description: 'Custom 3D avatars, animations, colors, and branded experiences'\n    },\n    {\n      id: 'analytics',\n      icon: '📊',\n      title: 'Smart Analytics',\n      description: 'Track engagement, scan rates, and measure your networking ROI'\n    }\n  ];\n\n  return (\n    <section ref={ref} className=\"py-20 bg-background relative overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `radial-gradient(circle at 25% 25%, #00f5ff 2px, transparent 2px),\n                           radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px)`,\n          backgroundSize: '50px 50px'\n        }} />\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-section gradient-text mb-6\">\n            Revolutionary Features\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto\">\n            Experience the future of networking with AR-powered business cards that work everywhere, \n            require no apps, and create unforgettable first impressions.\n          </p>\n        </motion.div>\n\n        {/* Interactive Feature Demo */}\n        <div className=\"grid lg:grid-cols-5 gap-8 items-start\">\n          {/* Feature Cards */}\n          <div className=\"lg:col-span-3 grid sm:grid-cols-2 gap-6\">\n            {features.map((feature, index) => (\n              <FeatureCard\n                key={feature.id}\n                {...feature}\n                delay={index * 0.1}\n                isActive={activeFeature === feature.id}\n                onClick={() => setActiveFeature(feature.id)}\n              />\n            ))}\n          </div>\n\n          {/* Feature Demo */}\n          <FeatureDemo activeFeature={activeFeature} />\n        </div>\n\n        {/* Additional Features Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mt-20 grid md:grid-cols-3 gap-8\"\n        >\n          {[\n            {\n              icon: '🌐',\n              title: 'No App Required',\n              description: 'Works in any browser on any device without downloads'\n            },\n            {\n              icon: '⚡',\n              title: 'Instant Loading',\n              description: 'Cards load in under 2 seconds with optimized 3D rendering'\n            },\n            {\n              icon: '🔒',\n              title: 'Privacy First',\n              description: 'Your data stays secure with end-to-end encryption'\n            }\n          ].map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              className=\"text-center p-6 bg-surface/50 backdrop-blur-sm border border-border rounded-lg hover:border-primary-cyan/50 transition-all duration-300\"\n            >\n              <div className=\"text-4xl mb-4\">{feature.icon}</div>\n              <h3 className=\"text-lg font-semibold text-text-primary mb-2\">{feature.title}</h3>\n              <p className=\"text-text-secondary\">{feature.description}</p>\n            </motion.div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,WAAW,KAAK,EAAE,OAAO,EAAE,iBACrF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,YAAY;YAAE,OAAO;QAAK;QAC1B,WAAW,CAAC,iEAAiE,EAC3E,WACI,4EACA,yDACJ;QACF,SAAS;;0BAET,8OAAC;gBAAI,WAAW,CAAC,oEAAoE,EACnF,WAAW,uBAAuB,aAClC;0BACC;;;;;;0BAEH,8OAAC;gBAAG,WAAU;0BAAgD;;;;;;0BAC9D,8OAAC;gBAAE,WAAU;0BAA+B;;;;;;;;;;;;AAIhD,MAAM,cAAc,CAAC,EAAE,aAAa,EAAE;IACpC,MAAM,cAAc;QAClB,IAAI;YACF,OAAO;YACP,aAAa;YACb,sBACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC,CAAC;wCAAG;wCAAG,CAAC;qCAAE;oCAAE,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCAAC;gCAC9C,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;oBAKlB;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;gCACvB,KAAK,GAAG,KAAK,AAAC,IAAI,IAAK,GAAG,CAAC,CAAC;4BAC9B;4BACA,SAAS;gCACP,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;gCAClB,OAAO;oCAAC;oCAAG;oCAAG;iCAAE;4BAClB;4BACA,YAAY;gCACV,UAAU;gCACV,OAAO,IAAI;gCACX,QAAQ;4BACV;2BAdK;;;;;;;;;;;QAmBf;QACA,SAAS;YACP,OAAO;YACP,aAAa;YACb,sBACE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC;wCAAY,WAAW,CAAC,WAAW,EAAE,KAAK,MAAM,KAAK,MAAM,aAAa,YAAY;uCAA3E;;;;;;;;;;0CAGd,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCAAC;gCAC9B,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;0CAEV,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;kCAIhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;0CAE7B,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;QAItD;QACA,eAAe;YACb,OAAO;YACP,aAAa;YACb,sBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,OAAO;4CACL,YAAY,CAAC,4BAA4B,EAAE,IAAI,GAAG,iBAAiB,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC;wCAChG;uCALK;;;;;;;;;;;sCAWX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD;oCAAC;oCAAQ;oCAAS;oCAAQ;iCAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,kBAC/C,8OAAC;wCAAiB,WAAU;kDACzB;uCADO;;;;;;;;;;;sCAOd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD;oCACC;oCACA;oCACA;oCACA;iCACD,CAAC,GAAG,CAAC,CAAC,UAAU,kBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;wCACV,OAAO;4CAAE,YAAY;wCAAS;uCAHzB;;;;;;;;;;;;;;;;;;;;;;QAUnB;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,sBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;wCAAI;qCAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,QAAQ;4CAAE;4CACrB,SAAS;gDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;4CAAC;4CAChC,YAAY;gDAAE,UAAU;gDAAG,OAAO,IAAI;4CAAI;4CAC1C,WAAU;2CAJL;;;;;;;;;;;;;;;;sCAWb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;gCACvD;oCACC;wCAAE,OAAO;wCAAe,OAAO;oCAAQ;oCACvC;wCAAE,OAAO;wCAAgB,OAAO;oCAAM;oCACtC;wCAAE,OAAO;wCAAmB,OAAO;oCAAM;oCACzC;wCAAE,OAAO;wCAAa,OAAO;oCAAM;iCACpC,CAAC,GAAG,CAAC,CAAC,MAAM,kBACX,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAK,WAAU;0DAAuB,KAAK,KAAK;;;;;;0DACjD,8OAAC;gDAAK,WAAU;0DAAmC,KAAK,KAAK;;;;;;;uCAFrD,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;QAShC;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QAET,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,WAAW,CAAC,cAAc,CAAC,KAAK;;;;;;kCAEnC,8OAAC;wBAAE,WAAU;kCACV,WAAW,CAAC,cAAc,CAAC,WAAW;;;;;;;;;;;;YAG1C,WAAW,CAAC,cAAc,CAAC,MAAM;;OAd7B;;;;;AAiBX;AAEe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,WAAW;QACf;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC;2FAC+D,CAAC;wBAClF,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAGhD,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;wCAEE,GAAG,OAAO;wCACX,OAAO,QAAQ;wCACf,UAAU,kBAAkB,QAAQ,EAAE;wCACtC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;uCAJrC,QAAQ,EAAE;;;;;;;;;;0CAUrB,8OAAC;gCAAY,eAAe;;;;;;;;;;;;kCAI9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET;4BACC;gCACE,MAAM;gCACN,OAAO;gCACP,aAAa;4BACf;4BACA;gCACE,MAAM;gCACN,OAAO;gCACP,aAAa;4BACf;4BACA;gCACE,MAAM;gCACN,OAAO;gCACP,aAAa;4BACf;yBACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAiB,QAAQ,IAAI;;;;;;kDAC5C,8OAAC;wCAAG,WAAU;kDAAgD,QAAQ,KAAK;;;;;;kDAC3E,8OAAC;wCAAE,WAAU;kDAAuB,QAAQ,WAAW;;;;;;;+BARlD,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;AAehC", "debugId": null}}, {"offset": {"line": 3519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/sections/PricingSection.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useState } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport Button from '../ui/Button';\n\nconst PricingCard = ({ plan, isPopular = false, delay = 0 }) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 50 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay }}\n      whileHover={{ \n        scale: 1.02,\n        rotateY: isHovered ? 5 : 0,\n      }}\n      onHoverStart={() => setIsHovered(true)}\n      onHoverEnd={() => setIsHovered(false)}\n      className={`relative p-8 rounded-xl border transition-all duration-300 ${\n        isPopular\n          ? 'bg-gradient-to-br from-primary-cyan/10 to-primary-purple/10 border-primary-cyan shadow-lg shadow-primary-cyan/25'\n          : 'bg-surface border-border hover:border-primary-cyan/50'\n      }`}\n      style={{\n        transformStyle: 'preserve-3d',\n      }}\n    >\n      {/* Popular Badge */}\n      {isPopular && (\n        <motion.div\n          initial={{ scale: 0 }}\n          animate={{ scale: 1 }}\n          transition={{ duration: 0.5, delay: delay + 0.2 }}\n          className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black px-4 py-1 rounded-full text-sm font-semibold\"\n        >\n          Most Popular\n        </motion.div>\n      )}\n\n      {/* Plan Header */}\n      <div className=\"text-center mb-8\">\n        <div className=\"text-4xl mb-4\">{plan.icon}</div>\n        <h3 className=\"text-2xl font-bold text-text-primary mb-2\">{plan.name}</h3>\n        <p className=\"text-text-secondary mb-4\">{plan.description}</p>\n        \n        {/* Price */}\n        <div className=\"mb-6\">\n          {plan.price === 'Free' ? (\n            <div className=\"text-4xl font-bold text-accent-green\">Free</div>\n          ) : (\n            <div className=\"flex items-baseline justify-center\">\n              <span className=\"text-4xl font-bold text-text-primary\">${plan.price}</span>\n              <span className=\"text-text-secondary ml-1\">/month</span>\n            </div>\n          )}\n          {plan.yearlyDiscount && (\n            <div className=\"text-sm text-accent-green mt-1\">\n              Save 20% with yearly billing\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Features */}\n      <div className=\"space-y-4 mb-8\">\n        {plan.features.map((feature, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, x: -20 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.4, delay: delay + index * 0.1 }}\n            className=\"flex items-start space-x-3\"\n          >\n            <div className={`flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5 ${\n              feature.included ? 'bg-accent-green text-black' : 'bg-border text-text-muted'\n            }`}>\n              {feature.included ? '✓' : '×'}\n            </div>\n            <span className={`text-sm ${\n              feature.included ? 'text-text-primary' : 'text-text-muted line-through'\n            }`}>\n              {feature.text}\n            </span>\n          </motion.div>\n        ))}\n      </div>\n\n      {/* CTA Button */}\n      <Button\n        variant={isPopular ? 'primary' : 'outline'}\n        size=\"lg\"\n        className=\"w-full\"\n        href=\"/signup\"\n      >\n        {plan.cta}\n      </Button>\n\n      {/* 3D Effect Overlay */}\n      <div \n        className=\"absolute inset-0 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n        style={{\n          background: 'linear-gradient(135deg, rgba(0,245,255,0.1) 0%, rgba(139,92,246,0.1) 100%)',\n          transform: 'translateZ(10px)',\n        }}\n      />\n    </motion.div>\n  );\n};\n\nexport default function PricingSection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  const plans = [\n    {\n      name: 'Basic AR',\n      icon: '🆓',\n      price: 'Free',\n      description: 'Perfect for trying out AR business cards',\n      cta: 'Start Free',\n      features: [\n        { text: '1 AR business card', included: true },\n        { text: 'Random preset effect', included: true },\n        { text: 'QR code sharing', included: true },\n        { text: 'Basic contact information', included: true },\n        { text: 'NameCardAI branding', included: true },\n        { text: 'Custom branding', included: false },\n        { text: 'Analytics dashboard', included: false },\n        { text: 'Team features', included: false },\n      ]\n    },\n    {\n      name: 'Pro AR',\n      icon: '⭐',\n      price: '19',\n      description: 'For professionals who want to stand out',\n      cta: 'Start Pro Trial',\n      yearlyDiscount: true,\n      features: [\n        { text: '3 AR business cards', included: true },\n        { text: 'Choose from 20+ AR effects', included: true },\n        { text: 'Custom intro video (30s)', included: true },\n        { text: 'QR + NFC + Camera sharing', included: true },\n        { text: 'Basic analytics', included: true },\n        { text: 'Remove NameCardAI branding', included: true },\n        { text: 'Priority support', included: true },\n        { text: 'Team collaboration', included: false },\n      ]\n    },\n    {\n      name: 'Premium AR',\n      icon: '🚀',\n      price: '39',\n      description: 'Full AR studio with unlimited customization',\n      cta: 'Go Premium',\n      yearlyDiscount: true,\n      features: [\n        { text: 'Unlimited AR business cards', included: true },\n        { text: 'Full customization suite', included: true },\n        { text: 'Custom 3D avatars', included: true },\n        { text: 'Extended intro videos (2min)', included: true },\n        { text: 'All sharing methods', included: true },\n        { text: 'Advanced analytics', included: true },\n        { text: 'Team collaboration', included: true },\n        { text: 'API access', included: true },\n      ]\n    },\n    {\n      name: 'Enterprise',\n      icon: '🏢',\n      price: '99',\n      description: 'For teams and organizations at scale',\n      cta: 'Contact Sales',\n      yearlyDiscount: true,\n      features: [\n        { text: 'Everything in Premium', included: true },\n        { text: 'White-label solution', included: true },\n        { text: 'Custom AR effect development', included: true },\n        { text: 'Bulk card management', included: true },\n        { text: 'SSO integration', included: true },\n        { text: 'Dedicated account manager', included: true },\n        { text: 'SLA guarantee', included: true },\n        { text: 'Custom integrations', included: true },\n      ]\n    }\n  ];\n\n  return (\n    <section ref={ref} className=\"py-20 bg-gradient-to-b from-surface to-background relative overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0\">\n        {/* Floating Orbs */}\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-32 h-32 rounded-full opacity-10\"\n            style={{\n              background: `radial-gradient(circle, ${i % 2 === 0 ? '#00f5ff' : '#8b5cf6'} 0%, transparent 70%)`,\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              x: [0, 100, 0],\n              y: [0, -100, 0],\n              scale: [1, 1.2, 1],\n            }}\n            transition={{\n              duration: 10 + i * 2,\n              repeat: Infinity,\n              ease: \"easeInOut\"\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-section gradient-text mb-6\">\n            Choose Your AR Experience\n          </h2>\n          <p className=\"text-xl text-text-secondary max-w-3xl mx-auto mb-8\">\n            From free AR cards to enterprise solutions, find the perfect plan to revolutionize your networking.\n          </p>\n          \n          {/* Billing Toggle */}\n          <div className=\"flex items-center justify-center space-x-4\">\n            <span className=\"text-text-secondary\">Monthly</span>\n            <div className=\"relative\">\n              <input type=\"checkbox\" className=\"sr-only\" />\n              <div className=\"w-12 h-6 bg-border rounded-full cursor-pointer\">\n                <div className=\"w-5 h-5 bg-primary-cyan rounded-full shadow-md transform transition-transform translate-x-0.5 translate-y-0.5\"></div>\n              </div>\n            </div>\n            <span className=\"text-text-secondary\">\n              Yearly <span className=\"text-accent-green text-sm\">(Save 20%)</span>\n            </span>\n          </div>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid lg:grid-cols-4 md:grid-cols-2 gap-8\">\n          {plans.map((plan, index) => (\n            <PricingCard\n              key={plan.name}\n              plan={plan}\n              isPopular={plan.name === 'Pro AR'}\n              delay={index * 0.1}\n            />\n          ))}\n        </div>\n\n        {/* FAQ Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"mt-20 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-text-primary mb-8\">\n            Frequently Asked Questions\n          </h3>\n          \n          <div className=\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n            {[\n              {\n                question: \"Can I upgrade or downgrade anytime?\",\n                answer: \"Yes! You can change your plan at any time. Upgrades take effect immediately, and downgrades apply at your next billing cycle.\"\n              },\n              {\n                question: \"Do you offer refunds?\",\n                answer: \"We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied, we'll refund your payment.\"\n              },\n              {\n                question: \"Is there a setup fee?\",\n                answer: \"No setup fees ever. You only pay the monthly or yearly subscription fee for your chosen plan.\"\n              },\n              {\n                question: \"Can I use my own domain?\",\n                answer: \"Yes! Premium and Enterprise plans include custom domain support for your AR business cards.\"\n              }\n            ].map((faq, index) => (\n              <motion.div\n                key={index}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                className=\"text-left p-6 bg-surface/50 backdrop-blur-sm border border-border rounded-lg\"\n              >\n                <h4 className=\"text-lg font-semibold text-text-primary mb-2\">\n                  {faq.question}\n                </h4>\n                <p className=\"text-text-secondary\">\n                  {faq.answer}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Enterprise CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          className=\"mt-16 text-center p-8 bg-gradient-to-r from-primary-cyan/10 to-primary-purple/10 border border-primary-cyan/30 rounded-xl\"\n        >\n          <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n            Need a Custom Solution?\n          </h3>\n          <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n            We work with enterprise clients to create custom AR experiences, integrations, and white-label solutions.\n          </p>\n          <Button variant=\"primary\" size=\"lg\" href=\"/contact\">\n            Contact Enterprise Sales\n          </Button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,YAAY,KAAK,EAAE,QAAQ,CAAC,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,YAAY;YACV,OAAO;YACP,SAAS,YAAY,IAAI;QAC3B;QACA,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;QAC/B,WAAW,CAAC,2DAA2D,EACrE,YACI,qHACA,yDACJ;QACF,OAAO;YACL,gBAAgB;QAClB;;YAGC,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;gBAAE;gBACpB,SAAS;oBAAE,OAAO;gBAAE;gBACpB,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;gBAChD,WAAU;0BACX;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAiB,KAAK,IAAI;;;;;;kCACzC,8OAAC;wBAAG,WAAU;kCAA6C,KAAK,IAAI;;;;;;kCACpE,8OAAC;wBAAE,WAAU;kCAA4B,KAAK,WAAW;;;;;;kCAGzD,8OAAC;wBAAI,WAAU;;4BACZ,KAAK,KAAK,KAAK,uBACd,8OAAC;gCAAI,WAAU;0CAAuC;;;;;qDAEtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAuC;4CAAE,KAAK,KAAK;;;;;;;kDACnE,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;4BAG9C,KAAK,cAAc,kBAClB,8OAAC;gCAAI,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;0BAQtD,8OAAC;gBAAI,WAAU;0BACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ,QAAQ;wBAAI;wBACxD,WAAU;;0CAEV,8OAAC;gCAAI,WAAW,CAAC,2EAA2E,EAC1F,QAAQ,QAAQ,GAAG,+BAA+B,6BAClD;0CACC,QAAQ,QAAQ,GAAG,MAAM;;;;;;0CAE5B,8OAAC;gCAAK,WAAW,CAAC,QAAQ,EACxB,QAAQ,QAAQ,GAAG,sBAAsB,gCACzC;0CACC,QAAQ,IAAI;;;;;;;uBAdV;;;;;;;;;;0BAqBX,8OAAC,iIAAA,CAAA,UAAM;gBACL,SAAS,YAAY,YAAY;gBACjC,MAAK;gBACL,WAAU;gBACV,MAAK;0BAEJ,KAAK,GAAG;;;;;;0BAIX,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY;oBACZ,WAAW;gBACb;;;;;;;;;;;;AAIR;AAEe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,KAAK;YACL,UAAU;gBACR;oBAAE,MAAM;oBAAsB,UAAU;gBAAK;gBAC7C;oBAAE,MAAM;oBAAwB,UAAU;gBAAK;gBAC/C;oBAAE,MAAM;oBAAmB,UAAU;gBAAK;gBAC1C;oBAAE,MAAM;oBAA6B,UAAU;gBAAK;gBACpD;oBAAE,MAAM;oBAAuB,UAAU;gBAAK;gBAC9C;oBAAE,MAAM;oBAAmB,UAAU;gBAAM;gBAC3C;oBAAE,MAAM;oBAAuB,UAAU;gBAAM;gBAC/C;oBAAE,MAAM;oBAAiB,UAAU;gBAAM;aAC1C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,KAAK;YACL,gBAAgB;YAChB,UAAU;gBACR;oBAAE,MAAM;oBAAuB,UAAU;gBAAK;gBAC9C;oBAAE,MAAM;oBAA8B,UAAU;gBAAK;gBACrD;oBAAE,MAAM;oBAA4B,UAAU;gBAAK;gBACnD;oBAAE,MAAM;oBAA6B,UAAU;gBAAK;gBACpD;oBAAE,MAAM;oBAAmB,UAAU;gBAAK;gBAC1C;oBAAE,MAAM;oBAA8B,UAAU;gBAAK;gBACrD;oBAAE,MAAM;oBAAoB,UAAU;gBAAK;gBAC3C;oBAAE,MAAM;oBAAsB,UAAU;gBAAM;aAC/C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,KAAK;YACL,gBAAgB;YAChB,UAAU;gBACR;oBAAE,MAAM;oBAA+B,UAAU;gBAAK;gBACtD;oBAAE,MAAM;oBAA4B,UAAU;gBAAK;gBACnD;oBAAE,MAAM;oBAAqB,UAAU;gBAAK;gBAC5C;oBAAE,MAAM;oBAAgC,UAAU;gBAAK;gBACvD;oBAAE,MAAM;oBAAuB,UAAU;gBAAK;gBAC9C;oBAAE,MAAM;oBAAsB,UAAU;gBAAK;gBAC7C;oBAAE,MAAM;oBAAsB,UAAU;gBAAK;gBAC7C;oBAAE,MAAM;oBAAc,UAAU;gBAAK;aACtC;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;YACb,KAAK;YACL,gBAAgB;YAChB,UAAU;gBACR;oBAAE,MAAM;oBAAyB,UAAU;gBAAK;gBAChD;oBAAE,MAAM;oBAAwB,UAAU;gBAAK;gBAC/C;oBAAE,MAAM;oBAAgC,UAAU;gBAAK;gBACvD;oBAAE,MAAM;oBAAwB,UAAU;gBAAK;gBAC/C;oBAAE,MAAM;oBAAmB,UAAU;gBAAK;gBAC1C;oBAAE,MAAM;oBAA6B,UAAU;gBAAK;gBACpD;oBAAE,MAAM;oBAAiB,UAAU;gBAAK;gBACxC;oBAAE,MAAM;oBAAuB,UAAU;gBAAK;aAC/C;QACH;KACD;IAED,qBACE,8OAAC;QAAQ,KAAK;QAAK,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;0BAEZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,wBAAwB,EAAE,IAAI,MAAM,IAAI,YAAY,UAAU,qBAAqB,CAAC;4BACjG,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAChC;wBACA,SAAS;4BACP,GAAG;gCAAC;gCAAG;gCAAK;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAK;6BAAE;4BACf,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU,KAAK,IAAI;4BACnB,QAAQ;4BACR,MAAM;wBACR;uBAhBK;;;;;;;;;;0BAqBX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAGhD,8OAAC;gCAAE,WAAU;0CAAqD;;;;;;0CAKlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,MAAK;gDAAW,WAAU;;;;;;0DACjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;kDAGnB,8OAAC;wCAAK,WAAU;;4CAAsB;0DAC7B,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;;;;;;;kCAMzD,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAEC,MAAM;gCACN,WAAW,KAAK,IAAI,KAAK;gCACzB,OAAO,QAAQ;+BAHV,KAAK,IAAI;;;;;;;;;;kCASpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;0CACZ;oCACC;wCACE,UAAU;wCACV,QAAQ;oCACV;oCACA;wCACE,UAAU;wCACV,QAAQ;oCACV;oCACA;wCACE,UAAU;wCACV,QAAQ;oCACV;oCACA;wCACE,UAAU;wCACV,QAAQ;oCACV;iCACD,CAAC,GAAG,CAAC,CAAC,KAAK,sBACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,WAAU;;0DAEV,8OAAC;gDAAG,WAAU;0DACX,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;;uCAVR;;;;;;;;;;;;;;;;kCAkBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,8OAAC,iIAAA,CAAA,UAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,MAAK;0CAAW;;;;;;;;;;;;;;;;;;;;;;;;AAO9D", "debugId": null}}, {"offset": {"line": 4233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/sections/CTASection.js"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { motion, useAnimation, useInView } from 'framer-motion';\nimport Button from '../ui/Button';\n\n// Particle System Component\nconst ParticleSystem = () => {\n  return (\n    <div className=\"absolute inset-0 overflow-hidden\">\n      {/* Floating Particles */}\n      {[...Array(20)].map((_, i) => (\n        <motion.div\n          key={i}\n          className=\"absolute w-1 h-1 bg-primary-cyan rounded-full opacity-60\"\n          style={{\n            left: `${Math.random() * 100}%`,\n            top: `${Math.random() * 100}%`,\n          }}\n          animate={{\n            y: [0, -100, 0],\n            x: [0, Math.random() * 50 - 25, 0],\n            opacity: [0, 1, 0],\n            scale: [0, 1, 0],\n          }}\n          transition={{\n            duration: 4 + Math.random() * 4,\n            repeat: Infinity,\n            delay: Math.random() * 4,\n            ease: \"easeInOut\"\n          }}\n        />\n      ))}\n      \n      {/* Smoke Effect */}\n      {[...Array(8)].map((_, i) => (\n        <motion.div\n          key={`smoke-${i}`}\n          className=\"absolute rounded-full opacity-20\"\n          style={{\n            width: `${20 + Math.random() * 40}px`,\n            height: `${20 + Math.random() * 40}px`,\n            background: `radial-gradient(circle, ${i % 2 === 0 ? '#00f5ff' : '#8b5cf6'} 0%, transparent 70%)`,\n            left: `${Math.random() * 100}%`,\n            top: `${80 + Math.random() * 20}%`,\n          }}\n          animate={{\n            y: [0, -200],\n            x: [0, Math.random() * 100 - 50],\n            scale: [0.5, 2],\n            opacity: [0.3, 0],\n          }}\n          transition={{\n            duration: 6 + Math.random() * 4,\n            repeat: Infinity,\n            delay: Math.random() * 6,\n            ease: \"easeOut\"\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// Stats Counter Component\nconst StatCounter = ({ end, label, prefix = '', suffix = '', delay = 0 }) => {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true });\n  const controls = useAnimation();\n\n  useEffect(() => {\n    if (isInView) {\n      controls.start({\n        opacity: 1,\n        scale: 1,\n        transition: { duration: 0.6, delay }\n      });\n    }\n  }, [isInView, controls, delay]);\n\n  return (\n    <motion.div\n      ref={ref}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={controls}\n      className=\"text-center\"\n    >\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={isInView ? { opacity: 1 } : {}}\n        transition={{ duration: 2, delay: delay + 0.5 }}\n        className=\"text-4xl font-bold gradient-text mb-2\"\n      >\n        {prefix}\n        <motion.span\n          initial={{ opacity: 0 }}\n          animate={isInView ? { opacity: 1 } : {}}\n          transition={{ duration: 1.5, delay: delay + 0.5 }}\n        >\n          {end}\n        </motion.span>\n        {suffix}\n      </motion.div>\n      <div className=\"text-text-secondary text-sm\">{label}</div>\n    </motion.div>\n  );\n};\n\nexport default function CTASection() {\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n  const controls = useAnimation();\n\n  useEffect(() => {\n    if (isInView) {\n      controls.start('visible');\n    }\n  }, [isInView, controls]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: { duration: 0.8, ease: \"easeOut\" }\n    }\n  };\n\n  const stats = [\n    { end: '7B+', label: 'Paper Cards Replaced', prefix: '', suffix: '' },\n    { end: '88%', label: 'Waste Reduction', prefix: '', suffix: '' },\n    { end: '70%', label: 'Higher Retention', prefix: '', suffix: '' },\n    { end: '0', label: 'Apps Required', prefix: '', suffix: '' }\n  ];\n\n  return (\n    <section \n      ref={ref}\n      className=\"relative py-20 bg-gradient-to-br from-background via-surface to-background overflow-hidden\"\n    >\n      {/* Background Effects */}\n      <ParticleSystem />\n      \n      {/* Animated Grid Background */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"grid grid-cols-8 grid-rows-6 h-full w-full\">\n          {[...Array(48)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"border border-primary-cyan/20\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: [0, 0.5, 0] }}\n              transition={{\n                duration: 4,\n                delay: i * 0.1,\n                repeat: Infinity,\n                repeatDelay: 3\n              }}\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate={controls}\n          className=\"text-center\"\n        >\n          {/* Main CTA Content */}\n          <motion.div variants={itemVariants} className=\"mb-16\">\n            <h2 className=\"text-section gradient-text mb-6\">\n              Ready to Reinvent Your Name?\n            </h2>\n            <p className=\"text-xl text-text-secondary max-w-3xl mx-auto mb-8\">\n              Join thousands of professionals who've already transformed their networking with AR-enhanced digital business cards. \n              <span className=\"text-primary-cyan font-semibold\"> Start your journey today.</span>\n            </p>\n          </motion.div>\n\n          {/* Stats Section */}\n          <motion.div \n            variants={itemVariants}\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16\"\n          >\n            {stats.map((stat, index) => (\n              <StatCounter key={stat.label} {...stat} delay={index * 0.2} />\n            ))}\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\"\n          >\n            <Button \n              size=\"xl\" \n              href=\"/demo\"\n              className=\"group relative overflow-hidden\"\n            >\n              <span className=\"relative z-10 flex items-center\">\n                <span className=\"mr-3 text-2xl\">🎮</span>\n                Try Interactive Demo\n                <motion.span\n                  className=\"ml-2\"\n                  animate={{ x: [0, 5, 0] }}\n                  transition={{ duration: 1.5, repeat: Infinity }}\n                >\n                  →\n                </motion.span>\n              </span>\n              {/* Button Glow Effect */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-primary-cyan/20 to-primary-purple/20\"\n                animate={{\n                  scale: [1, 1.05, 1],\n                  opacity: [0.5, 0.8, 0.5],\n                }}\n                transition={{ duration: 2, repeat: Infinity }}\n              />\n            </Button>\n            \n            <Button \n              variant=\"outline\" \n              size=\"xl\"\n              href=\"/signup\"\n              className=\"group\"\n            >\n              <span className=\"mr-3 text-2xl\">✨</span>\n              Start Free Today\n              <motion.div\n                className=\"ml-2 w-2 h-2 bg-accent-green rounded-full\"\n                animate={{ scale: [1, 1.5, 1] }}\n                transition={{ duration: 1, repeat: Infinity }}\n              />\n            </Button>\n          </motion.div>\n\n          {/* Trust Indicators */}\n          <motion.div \n            variants={itemVariants}\n            className=\"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-sm text-text-muted\"\n          >\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n              No Credit Card Required\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n              30-Day Money Back Guarantee\n            </div>\n            <div className=\"flex items-center\">\n              <span className=\"w-2 h-2 bg-accent-green rounded-full mr-2 animate-pulse\" />\n              Setup in Under 5 Minutes\n            </div>\n          </motion.div>\n\n          {/* Social Proof */}\n          <motion.div \n            variants={itemVariants}\n            className=\"mt-16 p-8 bg-surface/50 backdrop-blur-sm border border-border rounded-xl\"\n          >\n            <div className=\"flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0\">\n              <div className=\"text-left\">\n                <h3 className=\"text-lg font-semibold text-text-primary mb-2\">\n                  Join the AR Revolution\n                </h3>\n                <p className=\"text-text-secondary\">\n                  Be part of the future of professional networking\n                </p>\n              </div>\n              \n              <div className=\"flex items-center space-x-4\">\n                {/* Simulated User Avatars */}\n                <div className=\"flex -space-x-2\">\n                  {[...Array(5)].map((_, i) => (\n                    <motion.div\n                      key={i}\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ duration: 0.5, delay: i * 0.1 }}\n                      className=\"w-10 h-10 rounded-full border-2 border-background\"\n                      style={{\n                        background: `linear-gradient(135deg, hsl(${i * 60}, 70%, 60%), hsl(${i * 60 + 60}, 70%, 60%))`\n                      }}\n                    />\n                  ))}\n                </div>\n                <div className=\"text-left\">\n                  <div className=\"text-sm font-semibold text-text-primary\">1,000+ Users</div>\n                  <div className=\"text-xs text-text-secondary\">Already networking in AR</div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Final Tagline */}\n          <motion.div \n            variants={itemVariants}\n            className=\"mt-12\"\n          >\n            <p className=\"text-lg text-text-secondary italic\">\n              \"Make even an unknown freelancer feel like a keynote speaker.\"\n            </p>\n            <p className=\"text-sm text-text-muted mt-2\">\n              — NameCardAI Mission\n            </p>\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Bottom Gradient */}\n      <div className=\"absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-background to-transparent\" />\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,4BAA4B;AAC5B,MAAM,iBAAiB;IACrB,qBACE,8OAAC;QAAI,WAAU;;YAEZ;mBAAI,MAAM;aAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oBAChC;oBACA,SAAS;wBACP,GAAG;4BAAC;4BAAG,CAAC;4BAAK;yBAAE;wBACf,GAAG;4BAAC;4BAAG,KAAK,MAAM,KAAK,KAAK;4BAAI;yBAAE;wBAClC,SAAS;4BAAC;4BAAG;4BAAG;yBAAE;wBAClB,OAAO;4BAAC;4BAAG;4BAAG;yBAAE;oBAClB;oBACA,YAAY;wBACV,UAAU,IAAI,KAAK,MAAM,KAAK;wBAC9B,QAAQ;wBACR,OAAO,KAAK,MAAM,KAAK;wBACvB,MAAM;oBACR;mBAjBK;;;;;YAsBR;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,OAAO,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;wBACrC,QAAQ,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,EAAE,CAAC;wBACtC,YAAY,CAAC,wBAAwB,EAAE,IAAI,MAAM,IAAI,YAAY,UAAU,qBAAqB,CAAC;wBACjG,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAC/B,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oBACpC;oBACA,SAAS;wBACP,GAAG;4BAAC;4BAAG,CAAC;yBAAI;wBACZ,GAAG;4BAAC;4BAAG,KAAK,MAAM,KAAK,MAAM;yBAAG;wBAChC,OAAO;4BAAC;4BAAK;yBAAE;wBACf,SAAS;4BAAC;4BAAK;yBAAE;oBACnB;oBACA,YAAY;wBACV,UAAU,IAAI,KAAK,MAAM,KAAK;wBAC9B,QAAQ;wBACR,OAAO,KAAK,MAAM,KAAK;wBACvB,MAAM;oBACR;mBApBK,CAAC,MAAM,EAAE,GAAG;;;;;;;;;;;AAyB3B;AAEA,0BAA0B;AAC1B,MAAM,cAAc,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAAE;IACtE,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;IAAK;IAC7C,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC;gBACb,SAAS;gBACT,OAAO;gBACP,YAAY;oBAAE,UAAU;oBAAK;gBAAM;YACrC;QACF;IACF,GAAG;QAAC;QAAU;QAAU;KAAM;IAE9B,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;QACT,WAAU;;0BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS,WAAW;oBAAE,SAAS;gBAAE,IAAI,CAAC;gBACtC,YAAY;oBAAE,UAAU;oBAAG,OAAO,QAAQ;gBAAI;gBAC9C,WAAU;;oBAET;kCACD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS,WAAW;4BAAE,SAAS;wBAAE,IAAI,CAAC;wBACtC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;kCAE/C;;;;;;oBAEF;;;;;;;0BAEH,8OAAC;gBAAI,WAAU;0BAA+B;;;;;;;;;;;;AAGpD;AAEe,SAAS;IACtB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAC/D,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC;QACjB;IACF,GAAG;QAAC;QAAU;KAAS;IAEvB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAU;QAC/C;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,KAAK;YAAO,OAAO;YAAwB,QAAQ;YAAI,QAAQ;QAAG;QACpE;YAAE,KAAK;YAAO,OAAO;YAAmB,QAAQ;YAAI,QAAQ;QAAG;QAC/D;YAAE,KAAK;YAAO,OAAO;YAAoB,QAAQ;YAAI,QAAQ;QAAG;QAChE;YAAE,KAAK;YAAK,OAAO;YAAiB,QAAQ;YAAI,QAAQ;QAAG;KAC5D;IAED,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;;0BAGV,8OAAC;;;;;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;oCAAC;oCAAG;oCAAK;iCAAE;4BAAC;4BAChC,YAAY;gCACV,UAAU;gCACV,OAAO,IAAI;gCACX,QAAQ;gCACR,aAAa;4BACf;2BATK;;;;;;;;;;;;;;;0BAeb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,SAAS;oBACT,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;;wCAAqD;sDAEhE,8OAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;sCAKtD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;oCAA8B,GAAG,IAAI;oCAAE,OAAO,QAAQ;mCAArC,KAAK,KAAK;;;;;;;;;;sCAKhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC,iIAAA,CAAA,UAAM;oCACL,MAAK;oCACL,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;8DACd,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAS;8DAEzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,WAAU;oDACV,SAAS;wDAAE,GAAG;4DAAC;4DAAG;4DAAG;yDAAE;oDAAC;oDACxB,YAAY;wDAAE,UAAU;wDAAK,QAAQ;oDAAS;8DAC/C;;;;;;;;;;;;sDAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAM;iDAAE;gDACnB,SAAS;oDAAC;oDAAK;oDAAK;iDAAI;4CAC1B;4CACA,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;;;;;;;8CAIhD,8OAAC,iIAAA,CAAA,UAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;wCAAQ;sDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;;;;;;;;;;;;;;;;;;sCAMlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA4D;;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA4D;;;;;;;8CAG9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;;;;;wCAA4D;;;;;;;;;;;;;sCAMhF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+C;;;;;;0DAG7D,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;kDAKrC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,UAAU;4DAAK,OAAO,IAAI;wDAAI;wDAC5C,WAAU;wDACV,OAAO;4DACL,YAAY,CAAC,4BAA4B,EAAE,IAAI,GAAG,iBAAiB,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC;wDAChG;uDAPK;;;;;;;;;;0DAWX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAA0C;;;;;;kEACzD,8OAAC;wDAAI,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOrD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAA+B;;;;;;;;;;;;;;;;;;;;;;;0BAQlD,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB", "debugId": null}}]}