{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Header.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Logo = () => (\n  <div className=\"flex items-center space-x-3\">\n    {/* Custom SVG Logo */}\n    <motion.div\n      className=\"relative\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      <svg\n        width=\"40\"\n        height=\"40\"\n        viewBox=\"0 0 40 40\"\n        fill=\"none\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n        className=\"glow-effect\"\n      >\n        {/* Outer Ring */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"18\"\n          stroke=\"url(#gradient1)\"\n          strokeWidth=\"2\"\n          fill=\"none\"\n          className=\"animate-pulse-slow\"\n        />\n        \n        {/* Inner Card Shape */}\n        <rect\n          x=\"8\"\n          y=\"12\"\n          width=\"24\"\n          height=\"16\"\n          rx=\"3\"\n          fill=\"url(#gradient2)\"\n          className=\"opacity-80\"\n        />\n        \n        {/* AR Lines */}\n        <path\n          d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\"\n          stroke=\"#00f5ff\"\n          strokeWidth=\"1.5\"\n          strokeLinecap=\"round\"\n          className=\"animate-pulse\"\n        />\n        \n        {/* Center Dot */}\n        <circle\n          cx=\"20\"\n          cy=\"20\"\n          r=\"2\"\n          fill=\"#00f5ff\"\n          className=\"animate-ping\"\n        />\n        \n        {/* Gradients */}\n        <defs>\n          <linearGradient id=\"gradient1\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n            <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n          </linearGradient>\n          <linearGradient id=\"gradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n            <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n          </linearGradient>\n        </defs>\n      </svg>\n    </motion.div>\n    \n    {/* Brand Name */}\n    <motion.div\n      initial={{ opacity: 0, x: -20 }}\n      animate={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.5, delay: 0.2 }}\n    >\n      <h1 className=\"text-2xl font-bold gradient-text\">\n        NameCard<span className=\"text-primary-cyan\">AI</span>\n      </h1>\n    </motion.div>\n  </div>\n);\n\nconst Navigation = ({ isOpen, onClose }) => {\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n    { href: '/signup', label: 'Get Started' },\n  ];\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <>\n          {/* Mobile Backdrop */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden\"\n            onClick={onClose}\n          />\n          \n          {/* Mobile Menu */}\n          <motion.div\n            initial={{ x: '100%' }}\n            animate={{ x: 0 }}\n            exit={{ x: '100%' }}\n            transition={{ type: 'spring', damping: 25, stiffness: 200 }}\n            className=\"fixed top-0 right-0 h-full w-80 bg-surface/95 backdrop-blur-lg border-l border-border z-50 lg:hidden\"\n          >\n            <div className=\"flex flex-col h-full p-6\">\n              <div className=\"flex justify-between items-center mb-8\">\n                <Logo />\n                <button\n                  onClick={onClose}\n                  className=\"p-2 hover:bg-border rounded-lg transition-colors\"\n                >\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              \n              <nav className=\"flex-1\">\n                <ul className=\"space-y-4\">\n                  {navItems.map((item, index) => (\n                    <motion.li\n                      key={item.href}\n                      initial={{ opacity: 0, x: 20 }}\n                      animate={{ opacity: 1, x: 0 }}\n                      transition={{ delay: index * 0.1 }}\n                    >\n                      <Link\n                        href={item.href}\n                        onClick={onClose}\n                        className={`block px-4 py-3 rounded-lg transition-all duration-200 ${\n                          item.label === 'Get Started'\n                            ? 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold hover:shadow-lg hover:shadow-primary-cyan/25'\n                            : 'hover:bg-border hover:text-primary-cyan'\n                        }`}\n                      >\n                        {item.label}\n                      </Link>\n                    </motion.li>\n                  ))}\n                </ul>\n              </nav>\n            </div>\n          </motion.div>\n        </>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default function Header() {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { href: '/', label: 'Home' },\n    { href: '/demo', label: 'Demo' },\n    { href: '/pitch', label: 'Pitch' },\n    { href: '/why-us', label: 'Why Us' },\n    { href: '/roadmap', label: 'Roadmap' },\n  ];\n\n  return (\n    <>\n      <motion.header\n        initial={{ y: -100 }}\n        animate={{ y: 0 }}\n        transition={{ duration: 0.5 }}\n        className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n          isScrolled\n            ? 'bg-surface/80 backdrop-blur-lg border-b border-border shadow-lg'\n            : 'bg-transparent'\n        }`}\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16 lg:h-20\">\n            {/* Logo */}\n            <Link href=\"/\" className=\"flex-shrink-0\">\n              <Logo />\n            </Link>\n\n            {/* Desktop Navigation */}\n            <nav className=\"hidden lg:flex items-center space-x-8\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200 font-medium\"\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </nav>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Link\n                href=\"/signup\"\n                className=\"hidden sm:inline-flex items-center px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Get Started\n              </Link>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsMobileMenuOpen(true)}\n                className=\"lg:hidden p-2 hover:bg-border rounded-lg transition-colors\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      {/* Mobile Navigation */}\n      <Navigation isOpen={isMobileMenuOpen} onClose={() => setIsMobileMenuOpen(false)} />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,OAAO,kBACX,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8OAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAGV,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,eAAc;4BACd,WAAU;;;;;;sCAIZ,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,WAAU;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,8OAAC;oCAAe,IAAG;oCAAY,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC1D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;wBAAmC;sCACvC,8OAAC;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;;;;;;;AAMpD,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;IACrC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAW,OAAO;QAAc;KACzC;IAED,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC;;8BAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;oBACV,SAAS;;;;;;8BAIX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;oBAAO;oBACrB,SAAS;wBAAE,GAAG;oBAAE;oBAChB,MAAM;wBAAE,GAAG;oBAAO;oBAClB,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;;;;kDACD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CAER,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,QAAQ;4CAAI;sDAEjC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,SAAS;gDACT,WAAW,CAAC,uDAAuD,EACjE,KAAK,KAAK,KAAK,gBACX,+HACA,2CACJ;0DAED,KAAK,KAAK;;;;;;2CAdR,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BpC;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAO;QAC3B;YAAE,MAAM;YAAS,OAAO;QAAO;QAC/B;YAAE,MAAM;YAAU,OAAO;QAAQ;QACjC;YAAE,MAAM;YAAW,OAAO;QAAS;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;KACtC;IAED,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;gBAAI;gBACnB,SAAS;oBAAE,GAAG;gBAAE;gBAChB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,4DAA4D,EACtE,aACI,oEACA,kBACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;;;;;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAW,QAAQ;gBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;AAG/E", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/layout/Footer.js"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { motion } from 'framer-motion';\n\nconst SocialIcon = ({ href, icon, label }) => (\n  <motion.a\n    href={href}\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    className=\"p-3 bg-surface hover:bg-border rounded-lg transition-all duration-200 hover:shadow-lg hover:shadow-primary-cyan/25 group\"\n    whileHover={{ scale: 1.05 }}\n    whileTap={{ scale: 0.95 }}\n    aria-label={label}\n  >\n    <div className=\"w-5 h-5 text-text-secondary group-hover:text-primary-cyan transition-colors\">\n      {icon}\n    </div>\n  </motion.a>\n);\n\nconst FooterLink = ({ href, children, external = false }) => (\n  <Link\n    href={href}\n    target={external ? \"_blank\" : undefined}\n    rel={external ? \"noopener noreferrer\" : undefined}\n    className=\"text-text-secondary hover:text-primary-cyan transition-colors duration-200\"\n  >\n    {children}\n  </Link>\n);\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: \"Product\",\n      links: [\n        { href: \"/demo\", label: \"Live Demo\" },\n        { href: \"/pitch\", label: \"Pitch Deck\" },\n        { href: \"/why-us\", label: \"Why Choose Us\" },\n        { href: \"/roadmap\", label: \"Roadmap\" },\n      ]\n    },\n    {\n      title: \"Company\",\n      links: [\n        { href: \"/about\", label: \"About Us\" },\n        { href: \"/team\", label: \"Team\" },\n        { href: \"/careers\", label: \"Careers\" },\n        { href: \"/contact\", label: \"Contact\" },\n      ]\n    },\n    {\n      title: \"Resources\",\n      links: [\n        { href: \"/docs\", label: \"Documentation\" },\n        { href: \"/api\", label: \"API Reference\" },\n        { href: \"/support\", label: \"Support\" },\n        { href: \"/blog\", label: \"Blog\" },\n      ]\n    },\n    {\n      title: \"Legal\",\n      links: [\n        { href: \"/privacy\", label: \"Privacy Policy\" },\n        { href: \"/terms\", label: \"Terms of Service\" },\n        { href: \"/cookies\", label: \"Cookie Policy\" },\n        { href: \"/security\", label: \"Security\" },\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    {\n      href: \"https://twitter.com/namecardai\",\n      label: \"Twitter\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://linkedin.com/company/namecardai\",\n      label: \"LinkedIn\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://github.com/namecardai\",\n      label: \"GitHub\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n        </svg>\n      )\n    },\n    {\n      href: \"https://discord.gg/namecardai\",\n      label: \"Discord\",\n      icon: (\n        <svg fill=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1568 2.4189Z\"/>\n        </svg>\n      )\n    }\n  ];\n\n  return (\n    <footer className=\"bg-surface border-t border-border\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"flex items-center space-x-3 mb-4\">\n              {/* Logo SVG */}\n              <svg\n                width=\"32\"\n                height=\"32\"\n                viewBox=\"0 0 40 40\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"20\" cy=\"20\" r=\"18\" stroke=\"url(#footerGradient)\" strokeWidth=\"2\" fill=\"none\" />\n                <rect x=\"8\" y=\"12\" width=\"24\" height=\"16\" rx=\"3\" fill=\"url(#footerGradient2)\" />\n                <path d=\"M12 16 L16 12 M28 16 L24 12 M12 24 L16 28 M28 24 L24 28\" stroke=\"#00f5ff\" strokeWidth=\"1.5\" strokeLinecap=\"round\" />\n                <circle cx=\"20\" cy=\"20\" r=\"2\" fill=\"#00f5ff\" />\n                <defs>\n                  <linearGradient id=\"footerGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#00f5ff\" />\n                    <stop offset=\"100%\" stopColor=\"#8b5cf6\" />\n                  </linearGradient>\n                  <linearGradient id=\"footerGradient2\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                    <stop offset=\"0%\" stopColor=\"#1a1a1a\" />\n                    <stop offset=\"100%\" stopColor=\"#2a2a2a\" />\n                  </linearGradient>\n                </defs>\n              </svg>\n              <h3 className=\"text-xl font-bold gradient-text\">\n                NameCard<span className=\"text-primary-cyan\">AI</span>\n              </h3>\n            </div>\n            <p className=\"text-text-secondary mb-6 max-w-sm\">\n              Redefining professional identity sharing through creativity, technology, and meaningful digital presence.\n            </p>\n            <div className=\"flex space-x-3\">\n              {socialLinks.map((social) => (\n                <SocialIcon key={social.label} {...social} />\n              ))}\n            </div>\n          </div>\n\n          {/* Footer Links */}\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-text-primary font-semibold mb-4\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.href}>\n                    <FooterLink href={link.href}>{link.label}</FooterLink>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Newsletter Signup */}\n        <div className=\"border-t border-border pt-8 mb-8\">\n          <div className=\"max-w-md\">\n            <h4 className=\"text-text-primary font-semibold mb-2\">Stay Updated</h4>\n            <p className=\"text-text-secondary mb-4\">Get the latest updates on AR business cards and networking technology.</p>\n            <div className=\"flex space-x-3\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email\"\n                className=\"flex-1 px-4 py-2 bg-background border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n              />\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"px-6 py-2 bg-gradient-to-r from-primary-cyan to-primary-purple text-black font-semibold rounded-lg hover:shadow-lg hover:shadow-primary-cyan/25 transition-all duration-200\"\n              >\n                Subscribe\n              </motion.button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <div className=\"text-text-secondary text-sm\">\n            © {currentYear} NameCardAI. All rights reserved.\n          </div>\n          <div className=\"flex items-center space-x-6 text-sm\">\n            <FooterLink href=\"/privacy\">Privacy</FooterLink>\n            <FooterLink href=\"/terms\">Terms</FooterLink>\n            <FooterLink href=\"/cookies\">Cookies</FooterLink>\n            <span className=\"text-text-muted\">Made with ❤️ for the future of networking</span>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,iBACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;QACP,MAAM;QACN,QAAO;QACP,KAAI;QACJ,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY;kBAEZ,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAKP,MAAM,aAAa,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,KAAK,EAAE,iBACtD,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,QAAQ,WAAW,WAAW;QAC9B,KAAK,WAAW,wBAAwB;QACxC,WAAU;kBAET;;;;;;AAIU,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAY;gBACpC;oBAAE,MAAM;oBAAU,OAAO;gBAAa;gBACtC;oBAAE,MAAM;oBAAW,OAAO;gBAAgB;gBAC1C;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAU,OAAO;gBAAW;gBACpC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;gBAC/B;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;aACtC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAS,OAAO;gBAAgB;gBACxC;oBAAE,MAAM;oBAAQ,OAAO;gBAAgB;gBACvC;oBAAE,MAAM;oBAAY,OAAO;gBAAU;gBACrC;oBAAE,MAAM;oBAAS,OAAO;gBAAO;aAChC;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,OAAO;gBAAiB;gBAC5C;oBAAE,MAAM;oBAAU,OAAO;gBAAmB;gBAC5C;oBAAE,MAAM;oBAAY,OAAO;gBAAgB;gBAC3C;oBAAE,MAAM;oBAAa,OAAO;gBAAW;aACxC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;QACA;YACE,MAAM;YACN,OAAO;YACP,oBACE,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;0BAC/B,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;QAGd;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,OAAM;;8DAEN,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAuB,aAAY;oDAAI,MAAK;;;;;;8DAClF,8OAAC;oDAAK,GAAE;oDAAI,GAAE;oDAAK,OAAM;oDAAK,QAAO;oDAAK,IAAG;oDAAI,MAAK;;;;;;8DACtD,8OAAC;oDAAK,GAAE;oDAA0D,QAAO;oDAAU,aAAY;oDAAM,eAAc;;;;;;8DACnH,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;8DACnC,8OAAC;;sEACC,8OAAC;4DAAe,IAAG;4DAAiB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAC/D,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;sEAEhC,8OAAC;4DAAe,IAAG;4DAAkB,IAAG;4DAAK,IAAG;4DAAK,IAAG;4DAAO,IAAG;;8EAChE,8OAAC;oEAAK,QAAO;oEAAK,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,QAAO;oEAAO,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;4CAAG,WAAU;;gDAAkC;8DACtC,8OAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;;;;;;;8CAGhD,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAA+B,GAAG,MAAM;2CAAxB,OAAO,KAAK;;;;;;;;;;;;;;;;wBAMlC,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwC,QAAQ,KAAK;;;;;;kDACnE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC;oDAAW,MAAM,KAAK,IAAI;8DAAG,KAAK,KAAK;;;;;;+CADjC,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;;8BAc3B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CACxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;gCAA8B;gCACxC;gCAAY;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAW,MAAK;8CAAS;;;;;;8CAC1B,8OAAC;oCAAW,MAAK;8CAAW;;;;;;8CAC5B,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 1152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/ui/Button.js"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { forwardRef } from 'react';\n\nconst Button = forwardRef(({\n  children,\n  variant = 'primary',\n  size = 'md',\n  className = '',\n  disabled = false,\n  loading = false,\n  onClick,\n  href,\n  target,\n  rel,\n  ...props\n}, ref) => {\n  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background disabled:opacity-50 disabled:cursor-not-allowed';\n  \n  const variants = {\n    primary: 'bg-gradient-to-r from-primary-cyan to-primary-purple text-black hover:shadow-lg hover:shadow-primary-cyan/25 focus:ring-primary-cyan',\n    secondary: 'bg-surface border border-border text-text-primary hover:bg-border hover:border-primary-cyan focus:ring-primary-cyan',\n    outline: 'border border-primary-cyan text-primary-cyan hover:bg-primary-cyan hover:text-black focus:ring-primary-cyan',\n    ghost: 'text-text-primary hover:bg-surface hover:text-primary-cyan focus:ring-primary-cyan',\n    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',\n    success: 'bg-accent-green text-black hover:bg-green-600 focus:ring-accent-green',\n  };\n  \n  const sizes = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg',\n    xl: 'px-8 py-4 text-xl',\n  };\n  \n  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;\n  \n  const content = (\n    <>\n      {loading && (\n        <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\">\n          <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\" />\n          <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\" />\n        </svg>\n      )}\n      {children}\n    </>\n  );\n  \n  const motionProps = {\n    whileHover: disabled ? {} : { scale: 1.02 },\n    whileTap: disabled ? {} : { scale: 0.98 },\n    transition: { duration: 0.1 }\n  };\n  \n  if (href) {\n    return (\n      <motion.a\n        ref={ref}\n        href={href}\n        target={target}\n        rel={rel}\n        className={classes}\n        {...motionProps}\n        {...props}\n      >\n        {content}\n      </motion.a>\n    );\n  }\n  \n  return (\n    <motion.button\n      ref={ref}\n      className={classes}\n      disabled={disabled || loading}\n      onClick={onClick}\n      {...motionProps}\n      {...props}\n    >\n      {content}\n    </motion.button>\n  );\n});\n\nButton.displayName = 'Button';\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,QAAQ,EACR,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,KAAK,EACf,OAAO,EACP,IAAI,EACJ,MAAM,EACN,GAAG,EACH,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;IAEjF,MAAM,wBACJ;;YACG,yBACC,8OAAC;gBAAI,WAAU;gBAAkC,MAAK;gBAAO,SAAQ;;kCACnE,8OAAC;wBAAO,WAAU;wBAAa,IAAG;wBAAK,IAAG;wBAAK,GAAE;wBAAK,QAAO;wBAAe,aAAY;;;;;;kCACxF,8OAAC;wBAAK,WAAU;wBAAa,MAAK;wBAAe,GAAE;;;;;;;;;;;;YAGtD;;;IAIL,MAAM,cAAc;QAClB,YAAY,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QAC1C,UAAU,WAAW,CAAC,IAAI;YAAE,OAAO;QAAK;QACxC,YAAY;YAAE,UAAU;QAAI;IAC9B;IAEA,IAAI,MAAM;QACR,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,KAAK;YACL,WAAW;YACV,GAAG,WAAW;YACd,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,SAAS;QACR,GAAG,WAAW;QACd,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i2-d2-namecardai/src/components/demo/DemoEngine.js"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Button from '../ui/Button';\n\nexport default function DemoEngine() {\n  const [currentLevel, setCurrentLevel] = useState(1);\n  const [completedLevels, setCompletedLevels] = useState(new Set());\n  const [showInstructions, setShowInstructions] = useState(true);\n  const [demoData, setDemoData] = useState({\n    userName: '<PERSON> Johnson',\n    userTitle: 'AI Engineer & Blockchain Developer',\n    userCompany: 'QuantumTech Innovations',\n    userEmail: '<EMAIL>',\n    userPhone: '+****************',\n    website: 'alexjohnson.dev',\n    linkedin: 'linkedin.com/in/alexjohnson',\n    github: 'github.com/alexjohnson',\n    cardStyle: 'holographic',\n    cardColor: 'cyan',\n    arEffect: 'hologram',\n    backgroundPattern: 'neural',\n    avatarStyle: '3d',\n    scanCount: 0,\n    shareCount: 0,\n    achievements: ['AI Expert', 'Blockchain Pioneer', 'Tech Speaker'],\n    skills: ['React', 'AI/ML', 'Blockchain', 'WebXR'],\n    location: 'San Francisco, CA',\n    timezone: 'PST',\n    availability: 'Available for Projects'\n  });\n\n  const levels = [\n    {\n      id: 1,\n      title: 'Basic AR Card',\n      description: 'View your first 3D business card',\n      instruction: 'Welcome! This is your AR business card. Click on it to see the 3D effect in action.',\n    },\n    {\n      id: 2,\n      title: 'AR Camera View',\n      description: 'See how it looks in AR camera mode',\n      instruction: 'Now let\\'s see how your card appears through AR camera. This simulates the real-world experience.',\n    },\n    {\n      id: 3,\n      title: 'Customize Your Card',\n      description: 'Change colors, effects, and information',\n      instruction: 'Customize your card! Try different colors and effects.',\n    },\n    {\n      id: 4,\n      title: 'Sharing Methods',\n      description: 'Generate QR codes and sharing links',\n      instruction: 'Learn how to share your card. Generate QR codes and direct links.',\n    },\n    {\n      id: 5,\n      title: 'Analytics Dashboard',\n      description: 'Track scans and engagement',\n      instruction: 'See how your networking performs with real-time analytics.',\n    }\n  ];\n\n  const currentLevelData = levels.find(level => level.id === currentLevel);\n\n  const completeLevel = (levelId) => {\n    setCompletedLevels(prev => new Set([...prev, levelId]));\n    if (levelId === currentLevel && levelId < levels.length) {\n      setTimeout(() => setCurrentLevel(levelId + 1), 1000);\n    }\n  };\n\n  const resetDemo = () => {\n    setCurrentLevel(1);\n    setCompletedLevels(new Set());\n    setShowInstructions(true);\n    setDemoData(prev => ({\n      ...prev,\n      scanCount: 0,\n      shareCount: 0\n    }));\n  };\n\n  // Next-Level Futuristic AR Card Component\n  const ARCard = ({ data, onInteraction }) => {\n    const [isHovered, setIsHovered] = useState(false);\n    const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n    const [isFlipped, setIsFlipped] = useState(false);\n    const [activeLayer, setActiveLayer] = useState(0);\n    const [scanlinePosition, setScanlinePosition] = useState(0);\n    const cardRef = useRef(null);\n\n    // Advanced mouse tracking with smooth interpolation\n    const handleMouseMove = (e) => {\n      if (cardRef.current) {\n        const rect = cardRef.current.getBoundingClientRect();\n        const centerX = rect.left + rect.width / 2;\n        const centerY = rect.top + rect.height / 2;\n\n        const rawX = (e.clientX - centerX) / 10;\n        const rawY = (e.clientY - centerY) / 10;\n\n        setMousePosition({\n          x: Math.max(-25, Math.min(25, rawX)),\n          y: Math.max(-25, Math.min(25, rawY))\n        });\n      }\n    };\n\n    // Advanced theme system with multiple variants\n    const themes = {\n      cyan: {\n        primary: '#00f5ff',\n        secondary: '#0891b2',\n        accent: '#67e8f9',\n        gradient: 'linear-gradient(135deg, #00f5ff, #0891b2, #67e8f9)',\n        glow: '0 0 30px rgba(0, 245, 255, 0.6)',\n        particle: '#00f5ff'\n      },\n      purple: {\n        primary: '#8b5cf6',\n        secondary: '#7c3aed',\n        accent: '#a78bfa',\n        gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed, #a78bfa)',\n        glow: '0 0 30px rgba(139, 92, 246, 0.6)',\n        particle: '#8b5cf6'\n      },\n      green: {\n        primary: '#10b981',\n        secondary: '#059669',\n        accent: '#34d399',\n        gradient: 'linear-gradient(135deg, #10b981, #059669, #34d399)',\n        glow: '0 0 30px rgba(16, 185, 129, 0.6)',\n        particle: '#10b981'\n      },\n      orange: {\n        primary: '#f59e0b',\n        secondary: '#d97706',\n        accent: '#fbbf24',\n        gradient: 'linear-gradient(135deg, #f59e0b, #d97706, #fbbf24)',\n        glow: '0 0 30px rgba(245, 158, 11, 0.6)',\n        particle: '#f59e0b'\n      },\n      rainbow: {\n        primary: '#ff0080',\n        secondary: '#7928ca',\n        accent: '#ff4081',\n        gradient: 'linear-gradient(135deg, #ff0080, #7928ca, #ff4081, #00d4ff)',\n        glow: '0 0 30px rgba(255, 0, 128, 0.6)',\n        particle: '#ff0080'\n      }\n    };\n\n    const theme = themes[data.cardColor] || themes.cyan;\n\n    // Scanline animation effect\n    useEffect(() => {\n      if (isHovered) {\n        const interval = setInterval(() => {\n          setScanlinePosition(prev => (prev + 2) % 100);\n        }, 50);\n        return () => clearInterval(interval);\n      }\n    }, [isHovered]);\n\n    // Layer cycling for depth effect\n    useEffect(() => {\n      const interval = setInterval(() => {\n        setActiveLayer(prev => (prev + 1) % 3);\n      }, 3000);\n      return () => clearInterval(interval);\n    }, []);\n\n    return (\n      <div className=\"flex items-center justify-center min-h-[500px] relative\">\n        {/* Ambient Particles */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          {[...Array(20)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-1 h-1 rounded-full\"\n              style={{\n                background: theme.particle,\n                left: `${Math.random() * 100}%`,\n                top: `${Math.random() * 100}%`,\n              }}\n              animate={{\n                y: [0, -100, 0],\n                x: [0, Math.random() * 50 - 25, 0],\n                opacity: [0, 1, 0],\n                scale: [0, 1.5, 0],\n              }}\n              transition={{\n                duration: 4 + Math.random() * 4,\n                repeat: Infinity,\n                delay: Math.random() * 4,\n                ease: \"easeInOut\"\n              }}\n            />\n          ))}\n        </div>\n\n        {/* Main Card Container */}\n        <motion.div\n          ref={cardRef}\n          className=\"relative w-96 h-60 cursor-pointer\"\n          style={{\n            transform: `perspective(1200px) rotateX(${mousePosition.y}deg) rotateY(${mousePosition.x}deg) ${isFlipped ? 'rotateY(180deg)' : ''}`,\n            transformStyle: 'preserve-3d'\n          }}\n          onMouseMove={handleMouseMove}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => {\n            setIsHovered(false);\n            setMousePosition({ x: 0, y: 0 });\n          }}\n          onClick={() => {\n            setIsFlipped(!isFlipped);\n            onInteraction?.();\n          }}\n          whileHover={{ scale: 1.02 }}\n          animate={{\n            boxShadow: [theme.glow, `0 0 40px ${theme.primary}80`, theme.glow]\n          }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          {/* Front Face */}\n          <div\n            className=\"absolute inset-0 rounded-xl shadow-2xl border-2 overflow-hidden\"\n            style={{\n              background: `linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 30%, #2a2a2a 70%, #1a1a1a 100%)`,\n              borderColor: theme.primary,\n              boxShadow: isHovered ? theme.glow : '0 15px 35px rgba(0,0,0,0.4)',\n              backfaceVisibility: 'hidden'\n            }}\n          >\n            {/* Neural Network Background Pattern */}\n            <div className=\"absolute inset-0 opacity-10\">\n              <svg width=\"100%\" height=\"100%\" viewBox=\"0 0 400 250\">\n                {[...Array(12)].map((_, i) => (\n                  <motion.circle\n                    key={i}\n                    cx={50 + (i % 4) * 100}\n                    cy={50 + Math.floor(i / 4) * 75}\n                    r=\"2\"\n                    fill={theme.primary}\n                    animate={{\n                      opacity: [0.3, 1, 0.3],\n                      scale: [1, 1.5, 1]\n                    }}\n                    transition={{\n                      duration: 2,\n                      delay: i * 0.2,\n                      repeat: Infinity\n                    }}\n                  />\n                ))}\n                {[...Array(8)].map((_, i) => (\n                  <motion.line\n                    key={`line-${i}`}\n                    x1={50 + (i % 4) * 100}\n                    y1={50 + Math.floor(i / 4) * 75}\n                    x2={150 + (i % 3) * 100}\n                    y2={125 + (i % 2) * 75}\n                    stroke={theme.primary}\n                    strokeWidth=\"1\"\n                    animate={{\n                      opacity: [0.2, 0.8, 0.2]\n                    }}\n                    transition={{\n                      duration: 3,\n                      delay: i * 0.3,\n                      repeat: Infinity\n                    }}\n                  />\n                ))}\n              </svg>\n            </div>\n\n            {/* Advanced AR Corner Indicators */}\n            <div className=\"absolute top-3 left-3 w-8 h-8\">\n              <motion.div\n                className=\"absolute inset-0 border-l-3 border-t-3 rounded-tl-lg\"\n                style={{ borderColor: theme.primary }}\n                animate={{\n                  opacity: [0.5, 1, 0.5],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ duration: 2, repeat: Infinity }}\n              />\n              <div className=\"absolute top-1 left-1 w-2 h-2 rounded-full\" style={{ background: theme.primary }} />\n            </div>\n            <div className=\"absolute top-3 right-3 w-8 h-8\">\n              <motion.div\n                className=\"absolute inset-0 border-r-3 border-t-3 rounded-tr-lg\"\n                style={{ borderColor: theme.primary }}\n                animate={{\n                  opacity: [0.5, 1, 0.5],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}\n              />\n              <div className=\"absolute top-1 right-1 w-2 h-2 rounded-full\" style={{ background: theme.primary }} />\n            </div>\n            <div className=\"absolute bottom-3 left-3 w-8 h-8\">\n              <motion.div\n                className=\"absolute inset-0 border-l-3 border-b-3 rounded-bl-lg\"\n                style={{ borderColor: theme.primary }}\n                animate={{\n                  opacity: [0.5, 1, 0.5],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ duration: 2, repeat: Infinity, delay: 1 }}\n              />\n              <div className=\"absolute bottom-1 left-1 w-2 h-2 rounded-full\" style={{ background: theme.primary }} />\n            </div>\n            <div className=\"absolute bottom-3 right-3 w-8 h-8\">\n              <motion.div\n                className=\"absolute inset-0 border-r-3 border-b-3 rounded-br-lg\"\n                style={{ borderColor: theme.primary }}\n                animate={{\n                  opacity: [0.5, 1, 0.5],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ duration: 2, repeat: Infinity, delay: 1.5 }}\n              />\n              <div className=\"absolute bottom-1 right-1 w-2 h-2 rounded-full\" style={{ background: theme.primary }} />\n            </div>\n\n            {/* Scanning Line Effect */}\n            {isHovered && (\n              <motion.div\n                className=\"absolute left-0 right-0 h-0.5 pointer-events-none\"\n                style={{\n                  background: `linear-gradient(90deg, transparent, ${theme.primary}, transparent)`,\n                  top: `${scanlinePosition}%`,\n                  boxShadow: `0 0 10px ${theme.primary}`\n                }}\n                animate={{\n                  opacity: [0, 1, 0]\n                }}\n                transition={{\n                  duration: 0.5,\n                  repeat: Infinity\n                }}\n              />\n            )}\n\n            {/* Main Content */}\n            <div className=\"p-8 h-full flex flex-col justify-between relative z-10\">\n              {/* Header Section */}\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-start space-x-4\">\n                  {/* 3D Avatar */}\n                  <motion.div\n                    className=\"relative w-16 h-16 rounded-xl overflow-hidden\"\n                    style={{\n                      background: theme.gradient,\n                      transform: `rotateY(${mousePosition.x * 0.5}deg) rotateX(${mousePosition.y * 0.5}deg)`\n                    }}\n                    animate={{\n                      boxShadow: [\n                        `0 0 20px ${theme.primary}40`,\n                        `0 0 30px ${theme.primary}60`,\n                        `0 0 20px ${theme.primary}40`\n                      ]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    <div className=\"absolute inset-2 rounded-lg bg-black/20 flex items-center justify-center text-xl font-bold text-white\">\n                      {data.userName.split(' ').map(n => n[0]).join('')}\n                    </div>\n                    {/* Holographic overlay */}\n                    <motion.div\n                      className=\"absolute inset-0 rounded-xl\"\n                      style={{\n                        background: `linear-gradient(45deg, transparent 30%, ${theme.accent}20 50%, transparent 70%)`\n                      }}\n                      animate={{\n                        x: isHovered ? ['-100%', '100%'] : 0\n                      }}\n                      transition={{\n                        duration: 1.5,\n                        ease: \"easeInOut\",\n                        repeat: isHovered ? Infinity : 0\n                      }}\n                    />\n                  </motion.div>\n\n                  {/* Name and Title */}\n                  <div>\n                    <motion.h3\n                      className=\"text-xl font-bold text-white mb-1\"\n                      animate={{\n                        textShadow: [\n                          `0 0 10px ${theme.primary}40`,\n                          `0 0 20px ${theme.primary}60`,\n                          `0 0 10px ${theme.primary}40`\n                        ]\n                      }}\n                      transition={{ duration: 2, repeat: Infinity }}\n                    >\n                      {data.userName}\n                    </motion.h3>\n                    <p className=\"text-gray-300 text-sm mb-1 font-medium\">{data.userTitle}</p>\n                    <p className=\"text-gray-400 text-xs\">{data.userCompany}</p>\n\n                    {/* Status Indicator */}\n                    <div className=\"flex items-center space-x-2 mt-2\">\n                      <motion.div\n                        className=\"w-2 h-2 rounded-full\"\n                        style={{ background: theme.accent }}\n                        animate={{\n                          scale: [1, 1.3, 1],\n                          opacity: [0.7, 1, 0.7]\n                        }}\n                        transition={{ duration: 2, repeat: Infinity }}\n                      />\n                      <span className=\"text-xs text-gray-400\">{data.availability}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* QR Code Placeholder */}\n                <motion.div\n                  className=\"w-12 h-12 rounded-lg border-2 flex items-center justify-center text-xs font-bold\"\n                  style={{\n                    borderColor: theme.primary,\n                    background: `${theme.primary}10`,\n                    color: theme.primary\n                  }}\n                  whileHover={{\n                    scale: 1.1,\n                    boxShadow: `0 0 20px ${theme.primary}40`\n                  }}\n                >\n                  QR\n                </motion.div>\n              </div>\n\n              {/* Skills Tags */}\n              <div className=\"flex flex-wrap gap-2 my-4\">\n                {data.skills.slice(0, 4).map((skill, i) => (\n                  <motion.span\n                    key={skill}\n                    className=\"px-2 py-1 rounded-full text-xs font-medium border\"\n                    style={{\n                      borderColor: theme.primary + '40',\n                      background: theme.primary + '10',\n                      color: theme.primary\n                    }}\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    transition={{ delay: i * 0.1 }}\n                    whileHover={{\n                      scale: 1.05,\n                      background: theme.primary + '20'\n                    }}\n                  >\n                    {skill}\n                  </motion.span>\n                ))}\n              </div>\n\n              {/* Contact Information */}\n              <div className=\"space-y-2\">\n                <motion.div\n                  className=\"flex items-center space-x-3 text-sm text-gray-300\"\n                  whileHover={{ x: 5 }}\n                >\n                  <span style={{ color: theme.primary }}>📧</span>\n                  <span className=\"text-xs font-mono\">{data.userEmail}</span>\n                </motion.div>\n                <motion.div\n                  className=\"flex items-center space-x-3 text-sm text-gray-300\"\n                  whileHover={{ x: 5 }}\n                >\n                  <span style={{ color: theme.primary }}>📱</span>\n                  <span className=\"text-xs font-mono\">{data.userPhone}</span>\n                </motion.div>\n                <motion.div\n                  className=\"flex items-center space-x-3 text-sm text-gray-300\"\n                  whileHover={{ x: 5 }}\n                >\n                  <span style={{ color: theme.primary }}>🌐</span>\n                  <span className=\"text-xs font-mono\">{data.website}</span>\n                </motion.div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-3 mt-4\">\n                {[\n                  { platform: 'LinkedIn', icon: '💼', url: data.linkedin },\n                  { platform: 'GitHub', icon: '💻', url: data.github },\n                  { platform: 'Portfolio', icon: '🎨', url: data.website }\n                ].map((social, i) => (\n                  <motion.div\n                    key={social.platform}\n                    className=\"w-8 h-8 rounded-lg border flex items-center justify-center text-sm cursor-pointer\"\n                    style={{\n                      borderColor: theme.primary + '40',\n                      background: theme.primary + '10'\n                    }}\n                    whileHover={{\n                      scale: 1.1,\n                      background: theme.primary + '20',\n                      boxShadow: `0 0 15px ${theme.primary}40`\n                    }}\n                    whileTap={{ scale: 0.95 }}\n                    animate={{\n                      y: [0, -2, 0]\n                    }}\n                    transition={{\n                      duration: 2,\n                      delay: i * 0.3,\n                      repeat: Infinity\n                    }}\n                  >\n                    {social.icon}\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            {/* Holographic Sweep Effect */}\n            <motion.div\n              className=\"absolute inset-0 rounded-xl pointer-events-none\"\n              style={{\n                background: `linear-gradient(45deg, transparent 30%, ${theme.accent}15 50%, transparent 70%)`,\n              }}\n              animate={{\n                x: isHovered ? ['-100%', '100%'] : 0,\n                opacity: isHovered ? [0, 1, 0] : 0\n              }}\n              transition={{\n                duration: 2,\n                ease: \"easeInOut\",\n                repeat: isHovered ? Infinity : 0\n              }}\n            />\n\n            {/* Data Stream Effect */}\n            {isHovered && (\n              <div className=\"absolute inset-0 overflow-hidden rounded-xl pointer-events-none\">\n                {[...Array(5)].map((_, i) => (\n                  <motion.div\n                    key={i}\n                    className=\"absolute w-px h-full opacity-30\"\n                    style={{\n                      background: `linear-gradient(to bottom, transparent, ${theme.primary}, transparent)`,\n                      left: `${20 + i * 20}%`\n                    }}\n                    animate={{\n                      y: ['-100%', '100%']\n                    }}\n                    transition={{\n                      duration: 1.5,\n                      delay: i * 0.2,\n                      repeat: Infinity,\n                      ease: \"linear\"\n                    }}\n                  />\n                ))}\n              </div>\n            )}\n          </div>\n        </motion.div>\n\n        {/* Stats Display */}\n        <div className=\"absolute top-4 right-4 bg-surface/90 backdrop-blur-sm border border-border rounded-lg p-3\">\n          <div className=\"text-xs text-text-secondary mb-1\">Card Stats</div>\n          <div className=\"text-sm text-text-primary\">\n            Scans: <span style={{ color: cardColor }}>{data.scanCount}</span>\n          </div>\n          <div className=\"text-sm text-text-primary\">\n            Shares: <span style={{ color: cardColor }}>{data.shareCount}</span>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Simple Camera Simulator\n  const CameraSimulator = ({ data, onScan }) => {\n    const [isScanning, setIsScanning] = useState(false);\n    const [showCard, setShowCard] = useState(false);\n\n    const startScan = () => {\n      setIsScanning(true);\n      setTimeout(() => {\n        setIsScanning(false);\n        setShowCard(true);\n        onScan?.();\n      }, 2000);\n    };\n\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"text-center\">\n          <Button onClick={startScan} disabled={isScanning}>\n            {isScanning ? '🔍 Scanning...' : '📷 Start AR Scan'}\n          </Button>\n        </div>\n\n        <div className=\"relative w-full max-w-lg mx-auto aspect-video bg-black rounded-xl overflow-hidden border-2 border-border\">\n          {/* Simulated Camera Feed */}\n          <div className=\"relative w-full h-full bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900\">\n            {isScanning && (\n              <motion.div\n                className=\"absolute left-0 right-0 h-1 bg-primary-cyan\"\n                animate={{ top: ['0%', '100%'] }}\n                transition={{ duration: 2, ease: \"linear\" }}\n              />\n            )}\n\n            {showCard && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.5 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\n              >\n                <div className=\"w-48 h-32 bg-surface border-2 border-primary-cyan rounded-lg p-3\">\n                  <div className=\"text-primary-cyan text-sm font-bold\">{data.userName}</div>\n                  <div className=\"text-text-secondary text-xs\">{data.userTitle}</div>\n                  <div className=\"text-text-muted text-xs\">{data.userCompany}</div>\n                </div>\n              </motion.div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const renderCurrentComponent = () => {\n    switch (currentLevel) {\n      case 1:\n        return <ARCard data={demoData} onInteraction={() => completeLevel(1)} />;\n      case 2:\n        return (\n          <CameraSimulator \n            data={demoData} \n            onScan={() => {\n              setDemoData(prev => ({ ...prev, scanCount: prev.scanCount + 1 }));\n              completeLevel(2);\n            }}\n          />\n        );\n      case 3:\n        return (\n          <div className=\"space-y-8\">\n            <ARCard data={demoData} />\n\n            {/* Advanced Customization Panel */}\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-6\">Customize Your AR Card</h3>\n\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                {/* Color Themes */}\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-3\">Color Theme</h4>\n                  <div className=\"grid grid-cols-3 gap-3\">\n                    {['cyan', 'purple', 'green', 'orange', 'rainbow'].map(color => (\n                      <motion.button\n                        key={color}\n                        onClick={() => {\n                          setDemoData(prev => ({ ...prev, cardColor: color }));\n                          completeLevel(3);\n                        }}\n                        className={`h-16 rounded-lg border-2 transition-all relative overflow-hidden ${\n                          demoData.cardColor === color ? 'border-white scale-105' : 'border-transparent'\n                        }`}\n                        style={{\n                          background: color === 'cyan' ? 'linear-gradient(135deg, #00f5ff, #0891b2)' :\n                                    color === 'purple' ? 'linear-gradient(135deg, #8b5cf6, #7c3aed)' :\n                                    color === 'green' ? 'linear-gradient(135deg, #10b981, #059669)' :\n                                    color === 'orange' ? 'linear-gradient(135deg, #f59e0b, #d97706)' :\n                                    'linear-gradient(135deg, #ff0080, #7928ca, #00d4ff)'\n                        }}\n                        whileHover={{ scale: 1.05 }}\n                        whileTap={{ scale: 0.95 }}\n                      >\n                        <div className=\"absolute inset-0 flex items-center justify-center\">\n                          <span className=\"text-white font-bold text-xs capitalize\">{color}</span>\n                        </div>\n                        {demoData.cardColor === color && (\n                          <motion.div\n                            className=\"absolute inset-0 border-2 border-white rounded-lg\"\n                            animate={{\n                              boxShadow: ['0 0 0 0 rgba(255,255,255,0.7)', '0 0 0 4px rgba(255,255,255,0)', '0 0 0 0 rgba(255,255,255,0)']\n                            }}\n                            transition={{ duration: 1.5, repeat: Infinity }}\n                          />\n                        )}\n                      </motion.button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Card Styles */}\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-3\">Card Style</h4>\n                  <div className=\"space-y-2\">\n                    {[\n                      { id: 'holographic', name: 'Holographic', desc: 'Futuristic hologram effect' },\n                      { id: 'neural', name: 'Neural Network', desc: 'AI-inspired connections' },\n                      { id: 'quantum', name: 'Quantum', desc: 'Particle-based design' },\n                      { id: 'matrix', name: 'Matrix', desc: 'Digital rain effect' }\n                    ].map((style) => (\n                      <motion.button\n                        key={style.id}\n                        onClick={() => {\n                          setDemoData(prev => ({ ...prev, cardStyle: style.id }));\n                          completeLevel(3);\n                        }}\n                        className={`w-full text-left p-3 rounded-lg border transition-all ${\n                          demoData.cardStyle === style.id\n                            ? 'border-primary-cyan bg-primary-cyan/10'\n                            : 'border-border hover:border-primary-cyan/50'\n                        }`}\n                        whileHover={{ x: 5 }}\n                      >\n                        <div className=\"font-medium text-text-primary text-sm\">{style.name}</div>\n                        <div className=\"text-text-secondary text-xs\">{style.desc}</div>\n                      </motion.button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* AR Effects */}\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-3\">AR Effects</h4>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {[\n                      { id: 'hologram', name: 'Hologram', icon: '✨' },\n                      { id: 'particle', name: 'Particles', icon: '🌟' },\n                      { id: 'glow', name: 'Glow', icon: '💫' },\n                      { id: 'scan', name: 'Scan Lines', icon: '📡' }\n                    ].map((effect) => (\n                      <motion.button\n                        key={effect.id}\n                        onClick={() => {\n                          setDemoData(prev => ({ ...prev, arEffect: effect.id }));\n                          completeLevel(3);\n                        }}\n                        className={`p-3 rounded-lg border text-center transition-all ${\n                          demoData.arEffect === effect.id\n                            ? 'border-primary-cyan bg-primary-cyan/10'\n                            : 'border-border hover:border-primary-cyan/50'\n                        }`}\n                        whileHover={{ scale: 1.02 }}\n                        whileTap={{ scale: 0.98 }}\n                      >\n                        <div className=\"text-lg mb-1\">{effect.icon}</div>\n                        <div className=\"text-xs font-medium text-text-primary\">{effect.name}</div>\n                      </motion.button>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Personal Info */}\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-3\">Quick Edit</h4>\n                  <div className=\"space-y-3\">\n                    <input\n                      type=\"text\"\n                      placeholder=\"Your Name\"\n                      value={demoData.userName}\n                      onChange={(e) => setDemoData(prev => ({ ...prev, userName: e.target.value }))}\n                      className=\"w-full px-3 py-2 bg-background border border-border rounded text-text-primary text-sm focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Your Title\"\n                      value={demoData.userTitle}\n                      onChange={(e) => setDemoData(prev => ({ ...prev, userTitle: e.target.value }))}\n                      className=\"w-full px-3 py-2 bg-background border border-border rounded text-text-primary text-sm focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                    />\n                    <input\n                      type=\"text\"\n                      placeholder=\"Company\"\n                      value={demoData.userCompany}\n                      onChange={(e) => setDemoData(prev => ({ ...prev, userCompany: e.target.value }))}\n                      className=\"w-full px-3 py-2 bg-background border border-border rounded text-text-primary text-sm focus:outline-none focus:ring-2 focus:ring-primary-cyan focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-6 text-center\">\n                <motion.div\n                  className=\"inline-flex items-center space-x-2 text-sm text-accent-green\"\n                  animate={{ opacity: [0.7, 1, 0.7] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                >\n                  <span>✨</span>\n                  <span>Changes apply in real-time</span>\n                  <span>✨</span>\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        );\n      case 4:\n        return (\n          <div className=\"space-y-8\">\n            {/* Sharing Methods Grid */}\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {[\n                {\n                  id: 'qr',\n                  title: 'QR Code',\n                  icon: '📱',\n                  description: 'Instant scan sharing',\n                  color: 'cyan'\n                },\n                {\n                  id: 'nfc',\n                  title: 'NFC Tap',\n                  icon: '📡',\n                  description: 'Touch to share',\n                  color: 'purple'\n                },\n                {\n                  id: 'link',\n                  title: 'Direct Link',\n                  icon: '🔗',\n                  description: 'Copy & paste anywhere',\n                  color: 'green'\n                },\n                {\n                  id: 'ar',\n                  title: 'AR Broadcast',\n                  icon: '🌐',\n                  description: 'Spatial sharing',\n                  color: 'orange'\n                }\n              ].map((method, index) => (\n                <motion.div\n                  key={method.id}\n                  className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 text-center cursor-pointer\"\n                  whileHover={{ scale: 1.02, y: -5 }}\n                  whileTap={{ scale: 0.98 }}\n                  onClick={() => {\n                    setDemoData(prev => ({ ...prev, shareCount: prev.shareCount + 1 }));\n                    completeLevel(4);\n                  }}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <motion.div\n                    className=\"text-4xl mb-4\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      rotate: [0, 5, -5, 0]\n                    }}\n                    transition={{\n                      duration: 2,\n                      delay: index * 0.5,\n                      repeat: Infinity\n                    }}\n                  >\n                    {method.icon}\n                  </motion.div>\n                  <h3 className=\"text-lg font-bold text-text-primary mb-2\">{method.title}</h3>\n                  <p className=\"text-text-secondary text-sm mb-4\">{method.description}</p>\n\n                  {/* Demo specific content */}\n                  {method.id === 'qr' && (\n                    <div className=\"bg-white p-3 rounded-lg inline-block\">\n                      <div className=\"w-20 h-20 grid grid-cols-8 gap-px\">\n                        {[...Array(64)].map((_, i) => (\n                          <motion.div\n                            key={i}\n                            className={`aspect-square ${Math.random() > 0.5 ? 'bg-black' : 'bg-white'}`}\n                            initial={{ opacity: 0 }}\n                            animate={{ opacity: 1 }}\n                            transition={{ delay: i * 0.01 }}\n                          />\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {method.id === 'nfc' && (\n                    <motion.div\n                      className=\"w-16 h-16 mx-auto rounded-full border-2 border-primary-purple flex items-center justify-center\"\n                      animate={{\n                        borderColor: ['#8b5cf6', '#a78bfa', '#8b5cf6'],\n                        boxShadow: [\n                          '0 0 0 0 rgba(139, 92, 246, 0.7)',\n                          '0 0 0 10px rgba(139, 92, 246, 0)',\n                          '0 0 0 0 rgba(139, 92, 246, 0)'\n                        ]\n                      }}\n                      transition={{ duration: 2, repeat: Infinity }}\n                    >\n                      <span className=\"text-primary-purple\">📡</span>\n                    </motion.div>\n                  )}\n\n                  {method.id === 'link' && (\n                    <div className=\"bg-background border border-border rounded p-2\">\n                      <div className=\"text-xs font-mono text-accent-green\">\n                        namecardai.com/alex-j\n                      </div>\n                    </div>\n                  )}\n\n                  {method.id === 'ar' && (\n                    <motion.div\n                      className=\"relative w-16 h-16 mx-auto\"\n                      animate={{ rotateY: [0, 360] }}\n                      transition={{ duration: 4, repeat: Infinity, ease: \"linear\" }}\n                    >\n                      <div className=\"absolute inset-0 rounded-full border-2 border-accent-orange opacity-60\" />\n                      <div className=\"absolute inset-2 rounded-full border-2 border-accent-orange opacity-80\" />\n                      <div className=\"absolute inset-4 rounded-full bg-accent-orange\" />\n                    </motion.div>\n                  )}\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Advanced Sharing Features */}\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-6\">Advanced Sharing Features</h3>\n\n              <div className=\"grid md:grid-cols-3 gap-6\">\n                {/* Analytics Preview */}\n                <div className=\"text-center\">\n                  <motion.div\n                    className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-primary-cyan to-primary-purple flex items-center justify-center\"\n                    animate={{\n                      boxShadow: [\n                        '0 0 20px rgba(0, 245, 255, 0.3)',\n                        '0 0 30px rgba(139, 92, 246, 0.5)',\n                        '0 0 20px rgba(0, 245, 255, 0.3)'\n                      ]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    <span className=\"text-2xl\">📊</span>\n                  </motion.div>\n                  <h4 className=\"font-semibold text-text-primary mb-2\">Real-time Analytics</h4>\n                  <p className=\"text-text-secondary text-sm\">Track every scan, view, and interaction</p>\n                </div>\n\n                {/* Smart Targeting */}\n                <div className=\"text-center\">\n                  <motion.div\n                    className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-accent-green to-accent-orange flex items-center justify-center\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      rotate: [0, 180, 360]\n                    }}\n                    transition={{ duration: 3, repeat: Infinity }}\n                  >\n                    <span className=\"text-2xl\">🎯</span>\n                  </motion.div>\n                  <h4 className=\"font-semibold text-text-primary mb-2\">Smart Targeting</h4>\n                  <p className=\"text-text-secondary text-sm\">AI-powered audience insights</p>\n                </div>\n\n                {/* Cross-Platform */}\n                <div className=\"text-center\">\n                  <motion.div\n                    className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-accent-pink to-accent-blue flex items-center justify-center\"\n                    animate={{\n                      y: [0, -5, 0]\n                    }}\n                    transition={{ duration: 2, repeat: Infinity }}\n                  >\n                    <span className=\"text-2xl\">🌐</span>\n                  </motion.div>\n                  <h4 className=\"font-semibold text-text-primary mb-2\">Cross-Platform</h4>\n                  <p className=\"text-text-secondary text-sm\">Works on any device, anywhere</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Sharing Stats */}\n            <div className=\"grid grid-cols-3 gap-4\">\n              <motion.div\n                className=\"bg-surface border border-border rounded-lg p-4 text-center\"\n                whileHover={{ scale: 1.02 }}\n              >\n                <motion.div\n                  className=\"text-2xl font-bold text-primary-cyan\"\n                  animate={{ scale: [1, 1.1, 1] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                >\n                  {demoData.shareCount + 1247}\n                </motion.div>\n                <div className=\"text-sm text-text-secondary\">Total Shares</div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-surface border border-border rounded-lg p-4 text-center\"\n                whileHover={{ scale: 1.02 }}\n              >\n                <motion.div\n                  className=\"text-2xl font-bold text-accent-green\"\n                  animate={{ scale: [1, 1.1, 1] }}\n                  transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}\n                >\n                  98%\n                </motion.div>\n                <div className=\"text-sm text-text-secondary\">Success Rate</div>\n              </motion.div>\n\n              <motion.div\n                className=\"bg-surface border border-border rounded-lg p-4 text-center\"\n                whileHover={{ scale: 1.02 }}\n              >\n                <motion.div\n                  className=\"text-2xl font-bold text-accent-orange\"\n                  animate={{ scale: [1, 1.1, 1] }}\n                  transition={{ duration: 2, repeat: Infinity, delay: 1 }}\n                >\n                  2.3s\n                </motion.div>\n                <div className=\"text-sm text-text-secondary\">Avg. Load Time</div>\n              </motion.div>\n            </div>\n          </div>\n        );\n      case 5:\n        return (\n          <div className=\"space-y-8\">\n            {/* Real-time Analytics Dashboard */}\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-6\">Real-time Analytics Dashboard</h3>\n\n              {/* Key Metrics */}\n              <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8\">\n                {[\n                  {\n                    label: 'Total Scans',\n                    value: 1247 + demoData.scanCount,\n                    change: '+12%',\n                    color: 'primary-cyan',\n                    icon: '📱'\n                  },\n                  {\n                    label: 'Unique Views',\n                    value: 892 + demoData.shareCount,\n                    change: '+8%',\n                    color: 'accent-green',\n                    icon: '👥'\n                  },\n                  {\n                    label: 'Conversion Rate',\n                    value: '23%',\n                    change: '+5%',\n                    color: 'accent-orange',\n                    icon: '🎯'\n                  },\n                  {\n                    label: 'Avg. Time',\n                    value: '45s',\n                    change: '+15%',\n                    color: 'primary-purple',\n                    icon: '⏱️'\n                  }\n                ].map((metric, i) => (\n                  <motion.div\n                    key={metric.label}\n                    className=\"bg-surface border border-border rounded-lg p-4 text-center\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: i * 0.1 }}\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <div className=\"text-2xl mb-2\">{metric.icon}</div>\n                    <motion.div\n                      className={`text-2xl font-bold text-${metric.color} mb-1`}\n                      animate={{ scale: [1, 1.05, 1] }}\n                      transition={{ duration: 2, repeat: Infinity, delay: i * 0.5 }}\n                    >\n                      {metric.value}\n                    </motion.div>\n                    <div className=\"text-sm text-text-secondary mb-1\">{metric.label}</div>\n                    <div className=\"text-xs text-accent-green\">{metric.change} this week</div>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Interactive Chart */}\n              <div className=\"bg-background border border-border rounded-lg p-6 mb-6\">\n                <h4 className=\"text-lg font-semibold text-text-primary mb-4\">Scan Activity (Last 7 Days)</h4>\n                <div className=\"flex items-end space-x-2 h-40 mb-4\">\n                  {[40, 65, 30, 80, 55, 90, 70].map((height, i) => (\n                    <motion.div\n                      key={i}\n                      initial={{ height: 0 }}\n                      animate={{ height: `${height}%` }}\n                      transition={{ duration: 1, delay: i * 0.1 }}\n                      className=\"bg-gradient-to-t from-primary-cyan to-primary-purple rounded-t flex-1 min-w-8 cursor-pointer relative group\"\n                      whileHover={{ scale: 1.05 }}\n                    >\n                      {/* Tooltip */}\n                      <div className=\"absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity\">\n                        {height * 10} scans\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n                <div className=\"flex justify-between text-xs text-text-secondary\">\n                  {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map(day => (\n                    <span key={day}>{day}</span>\n                  ))}\n                </div>\n              </div>\n\n              {/* Engagement Breakdown */}\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"text-lg font-semibold text-text-primary mb-4\">Interaction Types</h4>\n                  <div className=\"space-y-3\">\n                    {[\n                      { label: 'Card Views', value: 85, color: 'primary-cyan' },\n                      { label: 'Contact Saves', value: 65, color: 'accent-green' },\n                      { label: 'Social Clicks', value: 45, color: 'accent-orange' },\n                      { label: 'Shares', value: 25, color: 'primary-purple' }\n                    ].map((item, i) => (\n                      <div key={item.label}>\n                        <div className=\"flex justify-between text-sm mb-1\">\n                          <span className=\"text-text-secondary\">{item.label}</span>\n                          <span className=\"text-text-primary font-semibold\">{item.value}%</span>\n                        </div>\n                        <div className=\"w-full bg-border rounded-full h-2\">\n                          <motion.div\n                            initial={{ width: 0 }}\n                            animate={{ width: `${item.value}%` }}\n                            transition={{ duration: 1, delay: i * 0.2 }}\n                            className={`h-2 rounded-full bg-${item.color}`}\n                          />\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div>\n                  <h4 className=\"text-lg font-semibold text-text-primary mb-4\">Geographic Distribution</h4>\n                  <div className=\"space-y-3\">\n                    {[\n                      { country: 'United States', percentage: 45, flag: '🇺🇸' },\n                      { country: 'United Kingdom', percentage: 22, flag: '🇬🇧' },\n                      { country: 'Germany', percentage: 15, flag: '🇩🇪' },\n                      { country: 'Japan', percentage: 18, flag: '🇯🇵' }\n                    ].map((country, i) => (\n                      <motion.div\n                        key={country.country}\n                        className=\"flex items-center justify-between p-2 bg-surface border border-border rounded\"\n                        initial={{ opacity: 0, x: -20 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: i * 0.1 }}\n                        whileHover={{ x: 5 }}\n                      >\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg\">{country.flag}</span>\n                          <span className=\"text-text-primary text-sm\">{country.country}</span>\n                        </div>\n                        <span className=\"text-primary-cyan font-semibold\">{country.percentage}%</span>\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* AI Insights */}\n            <div className=\"bg-gradient-to-r from-primary-cyan/10 to-primary-purple/10 border border-primary-cyan/30 rounded-xl p-6\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-4 flex items-center\">\n                <span className=\"mr-2\">🤖</span>\n                AI-Powered Insights\n              </h3>\n              <div className=\"grid md:grid-cols-2 gap-6\">\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-2\">Optimization Suggestions</h4>\n                  <ul className=\"space-y-2 text-sm text-text-secondary\">\n                    <li className=\"flex items-start space-x-2\">\n                      <span className=\"text-accent-green\">✓</span>\n                      <span>Peak engagement occurs at 2-4 PM PST</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <span className=\"text-accent-orange\">⚡</span>\n                      <span>Consider adding video content for 23% boost</span>\n                    </li>\n                    <li className=\"flex items-start space-x-2\">\n                      <span className=\"text-primary-cyan\">📱</span>\n                      <span>Mobile users prefer QR code sharing</span>\n                    </li>\n                  </ul>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-text-primary mb-2\">Predicted Growth</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-text-secondary\">Next Week</span>\n                      <span className=\"text-accent-green font-semibold\">+15% scans</span>\n                    </div>\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-text-secondary\">Next Month</span>\n                      <span className=\"text-accent-green font-semibold\">+45% engagement</span>\n                    </div>\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-text-secondary\">ROI Projection</span>\n                      <span className=\"text-primary-cyan font-semibold\">340% increase</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button onClick={() => completeLevel(5)} size=\"lg\">\n                📊 Complete Analytics Tour\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                📧 Email Report\n              </Button>\n              <Button variant=\"outline\" size=\"lg\">\n                📈 Export Data\n              </Button>\n            </div>\n          </div>\n        );\n      default:\n        return <div>Loading...</div>;\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-surface to-background py-12\">\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center mb-12\"\n        >\n          <h1 className=\"text-hero gradient-text mb-6\">\n            Interactive AR Demo\n          </h1>\n          <p className=\"text-xl text-text-secondary mb-8 max-w-3xl mx-auto\">\n            Experience the complete NameCardAI journey. Go through each level to see how AR business cards revolutionize networking.\n          </p>\n          \n          {/* Progress */}\n          <div className=\"max-w-md mx-auto mb-8\">\n            <div className=\"flex justify-between text-sm text-text-secondary mb-2\">\n              <span>Progress</span>\n              <span>{completedLevels.size}/{levels.length} Complete</span>\n            </div>\n            <div className=\"w-full bg-border rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-primary-cyan to-primary-purple h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${(completedLevels.size / levels.length) * 100}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n          </div>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-4 gap-8\">\n          {/* Level Navigation */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-6 sticky top-24\">\n              <h3 className=\"text-lg font-bold text-text-primary mb-4\">Demo Levels</h3>\n              <div className=\"space-y-3\">\n                {levels.map((level) => (\n                  <motion.div\n                    key={level.id}\n                    whileHover={{ scale: 1.02 }}\n                    className={`p-3 rounded-lg border cursor-pointer transition-all ${\n                      currentLevel === level.id\n                        ? 'bg-primary-cyan/10 border-primary-cyan shadow-lg shadow-primary-cyan/25'\n                        : completedLevels.has(level.id)\n                        ? 'bg-accent-green/10 border-accent-green'\n                        : 'bg-surface border-border hover:border-primary-cyan/50'\n                    }`}\n                    onClick={() => setCurrentLevel(level.id)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${\n                        completedLevels.has(level.id)\n                          ? 'bg-accent-green text-black'\n                          : currentLevel === level.id\n                          ? 'bg-primary-cyan text-black'\n                          : 'bg-border text-text-secondary'\n                      }`}>\n                        {completedLevels.has(level.id) ? '✓' : level.id}\n                      </div>\n                      <div className=\"flex-1\">\n                        <h4 className=\"font-semibold text-text-primary text-sm\">{level.title}</h4>\n                        <p className=\"text-xs text-text-secondary\">{level.description}</p>\n                      </div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n              \n              <div className=\"mt-6 pt-6 border-t border-border\">\n                <Button variant=\"outline\" size=\"sm\" onClick={resetDemo} className=\"w-full\">\n                  🔄 Reset Demo\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Demo Area */}\n          <div className=\"lg:col-span-3\">\n            <motion.div\n              key={currentLevel}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.5 }}\n              className=\"bg-surface/50 backdrop-blur-sm border border-border rounded-xl p-8 min-h-[600px] relative\"\n            >\n              {/* Level Header */}\n              <div className=\"mb-8\">\n                <div className=\"flex items-center space-x-3 mb-4\">\n                  <div className=\"w-10 h-10 bg-primary-cyan text-black rounded-full flex items-center justify-center font-bold\">\n                    {currentLevel}\n                  </div>\n                  <div>\n                    <h2 className=\"text-2xl font-bold text-text-primary\">\n                      {currentLevelData?.title}\n                    </h2>\n                    <p className=\"text-text-secondary\">\n                      {currentLevelData?.description}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Demo Component */}\n              <div className=\"relative\">\n                {renderCurrentComponent()}\n              </div>\n\n              {/* Instructions Overlay */}\n              <AnimatePresence>\n                {showInstructions && currentLevelData && (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                    className=\"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50\"\n                  >\n                    <motion.div\n                      initial={{ scale: 0.8, opacity: 0 }}\n                      animate={{ scale: 1, opacity: 1 }}\n                      exit={{ scale: 0.8, opacity: 0 }}\n                      className=\"bg-surface border border-border rounded-xl p-8 max-w-md mx-4 text-center\"\n                    >\n                      <div className=\"text-4xl mb-4\">💡</div>\n                      <h3 className=\"text-xl font-bold text-text-primary mb-4\">Instructions</h3>\n                      <p className=\"text-text-secondary mb-6\">{currentLevelData.instruction}</p>\n                      <div className=\"flex space-x-3 justify-center\">\n                        <Button onClick={() => setShowInstructions(false)}>Got it!</Button>\n                        <Button variant=\"ghost\" onClick={() => setShowInstructions(false)}>Skip Tutorial</Button>\n                      </div>\n                    </motion.div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Completion Message */}\n        <AnimatePresence>\n          {completedLevels.size === levels.length && (\n            <motion.div\n              initial={{ opacity: 0, y: 50 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -50 }}\n              className=\"mt-12 text-center bg-gradient-to-r from-accent-green/10 to-primary-cyan/10 border border-accent-green/30 rounded-xl p-8\"\n            >\n              <div className=\"text-6xl mb-4\">🎉</div>\n              <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n                Demo Complete!\n              </h3>\n              <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n                Congratulations! You've experienced the full NameCardAI journey. Ready to create your own AR business card?\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <Button size=\"lg\" href=\"/signup\">\n                  ✨ Start Your AR Journey\n                </Button>\n                <Button variant=\"outline\" size=\"lg\" onClick={resetDemo}>\n                  🔄 Try Demo Again\n                </Button>\n              </div>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,WAAW;QACX,aAAa;QACb,WAAW;QACX,WAAW;QACX,SAAS;QACT,UAAU;QACV,QAAQ;QACR,WAAW;QACX,WAAW;QACX,UAAU;QACV,mBAAmB;QACnB,aAAa;QACb,WAAW;QACX,YAAY;QACZ,cAAc;YAAC;YAAa;YAAsB;SAAe;QACjE,QAAQ;YAAC;YAAS;YAAS;YAAc;SAAQ;QACjD,UAAU;QACV,UAAU;QACV,cAAc;IAChB;IAEA,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,aAAa;QACf;KACD;IAED,MAAM,mBAAmB,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAE3D,MAAM,gBAAgB,CAAC;QACrB,mBAAmB,CAAA,OAAQ,IAAI,IAAI;mBAAI;gBAAM;aAAQ;QACrD,IAAI,YAAY,gBAAgB,UAAU,OAAO,MAAM,EAAE;YACvD,WAAW,IAAM,gBAAgB,UAAU,IAAI;QACjD;IACF;IAEA,MAAM,YAAY;QAChB,gBAAgB;QAChB,mBAAmB,IAAI;QACvB,oBAAoB;QACpB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW;gBACX,YAAY;YACd,CAAC;IACH;IAEA,0CAA0C;IAC1C,MAAM,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE;QACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;YAAE,GAAG;YAAG,GAAG;QAAE;QAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzD,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAEvB,oDAAoD;QACpD,MAAM,kBAAkB,CAAC;YACvB,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;gBAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;gBACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;gBAEzC,MAAM,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;gBACrC,MAAM,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,IAAI;gBAErC,iBAAiB;oBACf,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;oBAC9B,GAAG,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI;gBAChC;YACF;QACF;QAEA,+CAA+C;QAC/C,MAAM,SAAS;YACb,MAAM;gBACJ,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;YACA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;YACA,QAAQ;gBACN,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;YACA,SAAS;gBACP,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,MAAM;gBACN,UAAU;YACZ;QACF;QAEA,MAAM,QAAQ,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,OAAO,IAAI;QAEnD,4BAA4B;QAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,IAAI,WAAW;gBACb,MAAM,WAAW,YAAY;oBAC3B,oBAAoB,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;gBAC3C,GAAG;gBACH,OAAO,IAAM,cAAc;YAC7B;QACF,GAAG;YAAC;SAAU;QAEd,iCAAiC;QACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;YACR,MAAM,WAAW,YAAY;gBAC3B,eAAe,CAAA,OAAQ,CAAC,OAAO,CAAC,IAAI;YACtC,GAAG;YACH,OAAO,IAAM,cAAc;QAC7B,GAAG,EAAE;QAEL,qBACE,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,YAAY,MAAM,QAAQ;gCAC1B,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;gCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAChC;4BACA,SAAS;gCACP,GAAG;oCAAC;oCAAG,CAAC;oCAAK;iCAAE;gCACf,GAAG;oCAAC;oCAAG,KAAK,MAAM,KAAK,KAAK;oCAAI;iCAAE;gCAClC,SAAS;oCAAC;oCAAG;oCAAG;iCAAE;gCAClB,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCACV,UAAU,IAAI,KAAK,MAAM,KAAK;gCAC9B,QAAQ;gCACR,OAAO,KAAK,MAAM,KAAK;gCACvB,MAAM;4BACR;2BAlBK;;;;;;;;;;8BAwBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,WAAU;oBACV,OAAO;wBACL,WAAW,CAAC,4BAA4B,EAAE,cAAc,CAAC,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,YAAY,oBAAoB,IAAI;wBACpI,gBAAgB;oBAClB;oBACA,aAAa;oBACb,cAAc,IAAM,aAAa;oBACjC,cAAc;wBACZ,aAAa;wBACb,iBAAiB;4BAAE,GAAG;4BAAG,GAAG;wBAAE;oBAChC;oBACA,SAAS;wBACP,aAAa,CAAC;wBACd;oBACF;oBACA,YAAY;wBAAE,OAAO;oBAAK;oBAC1B,SAAS;wBACP,WAAW;4BAAC,MAAM,IAAI;4BAAE,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;4BAAE,MAAM,IAAI;yBAAC;oBACpE;oBACA,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAG5C,cAAA,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,2EAA2E,CAAC;4BACzF,aAAa,MAAM,OAAO;4BAC1B,WAAW,YAAY,MAAM,IAAI,GAAG;4BACpC,oBAAoB;wBACtB;;0CAGA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,OAAM;oCAAO,QAAO;oCAAO,SAAQ;;wCACrC;+CAAI,MAAM;yCAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gDAEZ,IAAI,KAAK,AAAC,IAAI,IAAK;gDACnB,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;gDAC7B,GAAE;gDACF,MAAM,MAAM,OAAO;gDACnB,SAAS;oDACP,SAAS;wDAAC;wDAAK;wDAAG;qDAAI;oDACtB,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;gDACpB;gDACA,YAAY;oDACV,UAAU;oDACV,OAAO,IAAI;oDACX,QAAQ;gDACV;+CAbK;;;;;wCAgBR;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDAEV,IAAI,KAAK,AAAC,IAAI,IAAK;gDACnB,IAAI,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK;gDAC7B,IAAI,MAAM,AAAC,IAAI,IAAK;gDACpB,IAAI,MAAM,AAAC,IAAI,IAAK;gDACpB,QAAQ,MAAM,OAAO;gDACrB,aAAY;gDACZ,SAAS;oDACP,SAAS;wDAAC;wDAAK;wDAAK;qDAAI;gDAC1B;gDACA,YAAY;oDACV,UAAU;oDACV,OAAO,IAAI;oDACX,QAAQ;gDACV;+CAdK,CAAC,KAAK,EAAE,GAAG;;;;;;;;;;;;;;;;0CAqBxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,OAAO;wCAAC;wCACpC,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB;wCACA,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;;;;;;kDAE9C,8OAAC;wCAAI,WAAU;wCAA6C,OAAO;4CAAE,YAAY,MAAM,OAAO;wCAAC;;;;;;;;;;;;0CAEjG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,OAAO;wCAAC;wCACpC,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB;wCACA,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,OAAO;wCAAI;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;wCAA8C,OAAO;4CAAE,YAAY,MAAM,OAAO;wCAAC;;;;;;;;;;;;0CAElG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,OAAO;wCAAC;wCACpC,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB;wCACA,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,OAAO;wCAAE;;;;;;kDAExD,8OAAC;wCAAI,WAAU;wCAAgD,OAAO;4CAAE,YAAY,MAAM,OAAO;wCAAC;;;;;;;;;;;;0CAEpG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,OAAO;4CAAE,aAAa,MAAM,OAAO;wCAAC;wCACpC,SAAS;4CACP,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;4CACtB,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;wCACpB;wCACA,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,OAAO;wCAAI;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;wCAAiD,OAAO;4CAAE,YAAY,MAAM,OAAO;wCAAC;;;;;;;;;;;;4BAIpG,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCACL,YAAY,CAAC,oCAAoC,EAAE,MAAM,OAAO,CAAC,cAAc,CAAC;oCAChF,KAAK,GAAG,iBAAiB,CAAC,CAAC;oCAC3B,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,EAAE;gCACxC;gCACA,SAAS;oCACP,SAAS;wCAAC;wCAAG;wCAAG;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;gCACV;;;;;;0CAKJ,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,OAAO;4DACL,YAAY,MAAM,QAAQ;4DAC1B,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,GAAG,IAAI,aAAa,EAAE,cAAc,CAAC,GAAG,IAAI,IAAI,CAAC;wDACxF;wDACA,SAAS;4DACP,WAAW;gEACT,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;gEAC7B,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;gEAC7B,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;6DAC9B;wDACH;wDACA,YAAY;4DAAE,UAAU;4DAAG,QAAQ;wDAAS;;0EAE5C,8OAAC;gEAAI,WAAU;0EACZ,KAAK,QAAQ,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;0EAGhD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,OAAO;oEACL,YAAY,CAAC,wCAAwC,EAAE,MAAM,MAAM,CAAC,wBAAwB,CAAC;gEAC/F;gEACA,SAAS;oEACP,GAAG,YAAY;wEAAC;wEAAS;qEAAO,GAAG;gEACrC;gEACA,YAAY;oEACV,UAAU;oEACV,MAAM;oEACN,QAAQ,YAAY,WAAW;gEACjC;;;;;;;;;;;;kEAKJ,8OAAC;;0EACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gEACR,WAAU;gEACV,SAAS;oEACP,YAAY;wEACV,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;wEAC7B,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;wEAC7B,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;qEAC9B;gEACH;gEACA,YAAY;oEAAE,UAAU;oEAAG,QAAQ;gEAAS;0EAE3C,KAAK,QAAQ;;;;;;0EAEhB,8OAAC;gEAAE,WAAU;0EAA0C,KAAK,SAAS;;;;;;0EACrE,8OAAC;gEAAE,WAAU;0EAAyB,KAAK,WAAW;;;;;;0EAGtD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,WAAU;wEACV,OAAO;4EAAE,YAAY,MAAM,MAAM;wEAAC;wEAClC,SAAS;4EACP,OAAO;gFAAC;gFAAG;gFAAK;6EAAE;4EAClB,SAAS;gFAAC;gFAAK;gFAAG;6EAAI;wEACxB;wEACA,YAAY;4EAAE,UAAU;4EAAG,QAAQ;wEAAS;;;;;;kFAE9C,8OAAC;wEAAK,WAAU;kFAAyB,KAAK,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAMhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,OAAO;oDACL,aAAa,MAAM,OAAO;oDAC1B,YAAY,GAAG,MAAM,OAAO,CAAC,EAAE,CAAC;oDAChC,OAAO,MAAM,OAAO;gDACtB;gDACA,YAAY;oDACV,OAAO;oDACP,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;gDAC1C;0DACD;;;;;;;;;;;;kDAMH,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,kBACnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDAEV,WAAU;gDACV,OAAO;oDACL,aAAa,MAAM,OAAO,GAAG;oDAC7B,YAAY,MAAM,OAAO,GAAG;oDAC5B,OAAO,MAAM,OAAO;gDACtB;gDACA,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,OAAO,IAAI;gDAAI;gDAC7B,YAAY;oDACV,OAAO;oDACP,YAAY,MAAM,OAAO,GAAG;gDAC9B;0DAEC;+CAfI;;;;;;;;;;kDAqBX,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;;kEAEnB,8OAAC;wDAAK,OAAO;4DAAE,OAAO,MAAM,OAAO;wDAAC;kEAAG;;;;;;kEACvC,8OAAC;wDAAK,WAAU;kEAAqB,KAAK,SAAS;;;;;;;;;;;;0DAErD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;;kEAEnB,8OAAC;wDAAK,OAAO;4DAAE,OAAO,MAAM,OAAO;wDAAC;kEAAG;;;;;;kEACvC,8OAAC;wDAAK,WAAU;kEAAqB,KAAK,SAAS;;;;;;;;;;;;0DAErD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,GAAG;gDAAE;;kEAEnB,8OAAC;wDAAK,OAAO;4DAAE,OAAO,MAAM,OAAO;wDAAC;kEAAG;;;;;;kEACvC,8OAAC;wDAAK,WAAU;kEAAqB,KAAK,OAAO;;;;;;;;;;;;;;;;;;kDAKrD,8OAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,UAAU;gDAAY,MAAM;gDAAM,KAAK,KAAK,QAAQ;4CAAC;4CACvD;gDAAE,UAAU;gDAAU,MAAM;gDAAM,KAAK,KAAK,MAAM;4CAAC;4CACnD;gDAAE,UAAU;gDAAa,MAAM;gDAAM,KAAK,KAAK,OAAO;4CAAC;yCACxD,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,OAAO;oDACL,aAAa,MAAM,OAAO,GAAG;oDAC7B,YAAY,MAAM,OAAO,GAAG;gDAC9B;gDACA,YAAY;oDACV,OAAO;oDACP,YAAY,MAAM,OAAO,GAAG;oDAC5B,WAAW,CAAC,SAAS,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;gDAC1C;gDACA,UAAU;oDAAE,OAAO;gDAAK;gDACxB,SAAS;oDACP,GAAG;wDAAC;wDAAG,CAAC;wDAAG;qDAAE;gDACf;gDACA,YAAY;oDACV,UAAU;oDACV,OAAO,IAAI;oDACX,QAAQ;gDACV;0DAEC,OAAO,IAAI;+CArBP,OAAO,QAAQ;;;;;;;;;;;;;;;;0CA4B5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,OAAO;oCACL,YAAY,CAAC,wCAAwC,EAAE,MAAM,MAAM,CAAC,wBAAwB,CAAC;gCAC/F;gCACA,SAAS;oCACP,GAAG,YAAY;wCAAC;wCAAS;qCAAO,GAAG;oCACnC,SAAS,YAAY;wCAAC;wCAAG;wCAAG;qCAAE,GAAG;gCACnC;gCACA,YAAY;oCACV,UAAU;oCACV,MAAM;oCACN,QAAQ,YAAY,WAAW;gCACjC;;;;;;4BAID,2BACC,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,OAAO;4CACL,YAAY,CAAC,wCAAwC,EAAE,MAAM,OAAO,CAAC,cAAc,CAAC;4CACpF,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;wCACzB;wCACA,SAAS;4CACP,GAAG;gDAAC;gDAAS;6CAAO;wCACtB;wCACA,YAAY;4CACV,UAAU;4CACV,OAAO,IAAI;4CACX,QAAQ;4CACR,MAAM;wCACR;uCAdK;;;;;;;;;;;;;;;;;;;;;8BAuBjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAmC;;;;;;sCAClD,8OAAC;4BAAI,WAAU;;gCAA4B;8CAClC,8OAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAU;8CAAI,KAAK,SAAS;;;;;;;;;;;;sCAE3D,8OAAC;4BAAI,WAAU;;gCAA4B;8CACjC,8OAAC;oCAAK,OAAO;wCAAE,OAAO;oCAAU;8CAAI,KAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;IAKrE;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;QACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAEzC,MAAM,YAAY;YAChB,cAAc;YACd,WAAW;gBACT,cAAc;gBACd,YAAY;gBACZ;YACF,GAAG;QACL;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;wBAAC,SAAS;wBAAW,UAAU;kCACnC,aAAa,mBAAmB;;;;;;;;;;;8BAIrC,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,KAAK;wCAAC;wCAAM;qCAAO;gCAAC;gCAC/B,YAAY;oCAAE,UAAU;oCAAG,MAAM;gCAAS;;;;;;4BAI7C,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuC,KAAK,QAAQ;;;;;;sDACnE,8OAAC;4CAAI,WAAU;sDAA+B,KAAK,SAAS;;;;;;sDAC5D,8OAAC;4CAAI,WAAU;sDAA2B,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQ1E;IAEA,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC;oBAAO,MAAM;oBAAU,eAAe,IAAM,cAAc;;;;;;YACpE,KAAK;gBACH,qBACE,8OAAC;oBACC,MAAM;oBACN,QAAQ;wBACN,YAAY,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,WAAW,KAAK,SAAS,GAAG;4BAAE,CAAC;wBAC/D,cAAc;oBAChB;;;;;;YAGN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAO,MAAM;;;;;;sCAGd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAQ;wDAAU;wDAAS;wDAAU;qDAAU,CAAC,GAAG,CAAC,CAAA,sBACpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DAEZ,SAAS;gEACP,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW;oEAAM,CAAC;gEAClD,cAAc;4DAChB;4DACA,WAAW,CAAC,iEAAiE,EAC3E,SAAS,SAAS,KAAK,QAAQ,2BAA2B,sBAC1D;4DACF,OAAO;gEACL,YAAY,UAAU,SAAS,8CACrB,UAAU,WAAW,8CACrB,UAAU,UAAU,8CACpB,UAAU,WAAW,8CACrB;4DACZ;4DACA,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;;8EAExB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAA2C;;;;;;;;;;;gEAE5D,SAAS,SAAS,KAAK,uBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,WAAU;oEACV,SAAS;wEACP,WAAW;4EAAC;4EAAiC;4EAAiC;yEAA8B;oEAC9G;oEACA,YAAY;wEAAE,UAAU;wEAAK,QAAQ;oEAAS;;;;;;;2DA3B7C;;;;;;;;;;;;;;;;sDAoCb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,IAAI;4DAAe,MAAM;4DAAe,MAAM;wDAA6B;wDAC7E;4DAAE,IAAI;4DAAU,MAAM;4DAAkB,MAAM;wDAA0B;wDACxE;4DAAE,IAAI;4DAAW,MAAM;4DAAW,MAAM;wDAAwB;wDAChE;4DAAE,IAAI;4DAAU,MAAM;4DAAU,MAAM;wDAAsB;qDAC7D,CAAC,GAAG,CAAC,CAAC,sBACL,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DAEZ,SAAS;gEACP,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,MAAM,EAAE;oEAAC,CAAC;gEACrD,cAAc;4DAChB;4DACA,WAAW,CAAC,sDAAsD,EAChE,SAAS,SAAS,KAAK,MAAM,EAAE,GAC3B,2CACA,8CACJ;4DACF,YAAY;gEAAE,GAAG;4DAAE;;8EAEnB,8OAAC;oEAAI,WAAU;8EAAyC,MAAM,IAAI;;;;;;8EAClE,8OAAC;oEAAI,WAAU;8EAA+B,MAAM,IAAI;;;;;;;2DAbnD,MAAM,EAAE;;;;;;;;;;;;;;;;sDAoBrB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,IAAI;4DAAY,MAAM;4DAAY,MAAM;wDAAI;wDAC9C;4DAAE,IAAI;4DAAY,MAAM;4DAAa,MAAM;wDAAK;wDAChD;4DAAE,IAAI;4DAAQ,MAAM;4DAAQ,MAAM;wDAAK;wDACvC;4DAAE,IAAI;4DAAQ,MAAM;4DAAc,MAAM;wDAAK;qDAC9C,CAAC,GAAG,CAAC,CAAC,uBACL,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4DAEZ,SAAS;gEACP,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,OAAO,EAAE;oEAAC,CAAC;gEACrD,cAAc;4DAChB;4DACA,WAAW,CAAC,iDAAiD,EAC3D,SAAS,QAAQ,KAAK,OAAO,EAAE,GAC3B,2CACA,8CACJ;4DACF,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,UAAU;gEAAE,OAAO;4DAAK;;8EAExB,8OAAC;oEAAI,WAAU;8EAAgB,OAAO,IAAI;;;;;;8EAC1C,8OAAC;oEAAI,WAAU;8EAAyC,OAAO,IAAI;;;;;;;2DAd9D,OAAO,EAAE;;;;;;;;;;;;;;;;sDAqBtB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,QAAQ;4DACxB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC3E,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC5E,WAAU;;;;;;sEAEZ,8OAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC9E,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCAAC;wCAClC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;;0DAE5C,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMlB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,IAAI;oCACJ,OAAO;oCACP,MAAM;oCACN,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,IAAI;oCACJ,OAAO;oCACP,MAAM;oCACN,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,IAAI;oCACJ,OAAO;oCACP,MAAM;oCACN,aAAa;oCACb,OAAO;gCACT;gCACA;oCACE,IAAI;oCACJ,OAAO;oCACP,MAAM;oCACN,aAAa;oCACb,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,YAAY;wCAAE,OAAO;wCAAM,GAAG,CAAC;oCAAE;oCACjC,UAAU;wCAAE,OAAO;oCAAK;oCACxB,SAAS;wCACP,YAAY,CAAA,OAAQ,CAAC;gDAAE,GAAG,IAAI;gDAAE,YAAY,KAAK,UAAU,GAAG;4CAAE,CAAC;wCACjE,cAAc;oCAChB;oCACA,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;;sDAEjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,QAAQ;oDAAC;oDAAG;oDAAG,CAAC;oDAAG;iDAAE;4CACvB;4CACA,YAAY;gDACV,UAAU;gDACV,OAAO,QAAQ;gDACf,QAAQ;4CACV;sDAEC,OAAO,IAAI;;;;;;sDAEd,8OAAC;4CAAG,WAAU;sDAA4C,OAAO,KAAK;;;;;;sDACtE,8OAAC;4CAAE,WAAU;sDAAoC,OAAO,WAAW;;;;;;wCAGlE,OAAO,EAAE,KAAK,sBACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,WAAW,CAAC,cAAc,EAAE,KAAK,MAAM,KAAK,MAAM,aAAa,YAAY;wDAC3E,SAAS;4DAAE,SAAS;wDAAE;wDACtB,SAAS;4DAAE,SAAS;wDAAE;wDACtB,YAAY;4DAAE,OAAO,IAAI;wDAAK;uDAJzB;;;;;;;;;;;;;;;wCAWd,OAAO,EAAE,KAAK,uBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,aAAa;oDAAC;oDAAW;oDAAW;iDAAU;gDAC9C,WAAW;oDACT;oDACA;oDACA;iDACD;4CACH;4CACA,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;sDAE5C,cAAA,8OAAC;gDAAK,WAAU;0DAAsB;;;;;;;;;;;wCAIzC,OAAO,EAAE,KAAK,wBACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;;;;;;wCAMxD,OAAO,EAAE,KAAK,sBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;oDAAC;oDAAG;iDAAI;4CAAC;4CAC7B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,MAAM;4CAAS;;8DAE5D,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;mCA/Ed,OAAO,EAAE;;;;;;;;;;sCAuFpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAEzD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,WAAW;4DACT;4DACA;4DACA;yDACD;oDACH;oDACA,YAAY;wDAAE,UAAU;wDAAG,QAAQ;oDAAS;8DAE5C,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;sDAI7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,OAAO;4DAAC;4DAAG;4DAAK;yDAAE;wDAClB,QAAQ;4DAAC;4DAAG;4DAAK;yDAAI;oDACvB;oDACA,YAAY;wDAAE,UAAU;wDAAG,QAAQ;oDAAS;8DAE5C,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;sDAI7C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDACP,GAAG;4DAAC;4DAAG,CAAC;4DAAG;yDAAE;oDACf;oDACA,YAAY;wDAAE,UAAU;wDAAG,QAAQ;oDAAS;8DAE5C,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;sDAE3C,SAAS,UAAU,GAAG;;;;;;sDAEzB,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAG/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,OAAO;4CAAI;sDACzD;;;;;;sDAGD,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAG/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;;sDAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CAAC;4CAC9B,YAAY;gDAAE,UAAU;gDAAG,QAAQ;gDAAU,OAAO;4CAAE;sDACvD;;;;;;sDAGD,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;;;;;;;YAKvD,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CACE,OAAO;4CACP,OAAO,OAAO,SAAS,SAAS;4CAChC,QAAQ;4CACR,OAAO;4CACP,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,OAAO,MAAM,SAAS,UAAU;4CAChC,QAAQ;4CACR,OAAO;4CACP,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,OAAO;4CACP,QAAQ;4CACR,OAAO;4CACP,MAAM;wCACR;wCACA;4CACE,OAAO;4CACP,OAAO;4CACP,QAAQ;4CACR,OAAO;4CACP,MAAM;wCACR;qCACD,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,IAAI;4CAAI;4CAC7B,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC;oDAAI,WAAU;8DAAiB,OAAO,IAAI;;;;;;8DAC3C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAW,CAAC,wBAAwB,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC;oDACzD,SAAS;wDAAE,OAAO;4DAAC;4DAAG;4DAAM;yDAAE;oDAAC;oDAC/B,YAAY;wDAAE,UAAU;wDAAG,QAAQ;wDAAU,OAAO,IAAI;oDAAI;8DAE3D,OAAO,KAAK;;;;;;8DAEf,8OAAC;oDAAI,WAAU;8DAAoC,OAAO,KAAK;;;;;;8DAC/D,8OAAC;oDAAI,WAAU;;wDAA6B,OAAO,MAAM;wDAAC;;;;;;;;2CAhBrD,OAAO,KAAK;;;;;;;;;;8CAsBvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA+C;;;;;;sDAC7D,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAI;gDAAI;gDAAI;gDAAI;gDAAI;gDAAI;6CAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,kBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,SAAS;wDAAE,QAAQ,GAAG,OAAO,CAAC,CAAC;oDAAC;oDAChC,YAAY;wDAAE,UAAU;wDAAG,OAAO,IAAI;oDAAI;oDAC1C,WAAU;oDACV,YAAY;wDAAE,OAAO;oDAAK;8DAG1B,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,SAAS;4DAAG;;;;;;;mDATV;;;;;;;;;;sDAcX,8OAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAO;gDAAO;gDAAO;gDAAO;gDAAO;gDAAO;6CAAM,CAAC,GAAG,CAAC,CAAA,oBACrD,8OAAC;8DAAgB;mDAAN;;;;;;;;;;;;;;;;8CAMjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,OAAO;4DAAc,OAAO;4DAAI,OAAO;wDAAe;wDACxD;4DAAE,OAAO;4DAAiB,OAAO;4DAAI,OAAO;wDAAe;wDAC3D;4DAAE,OAAO;4DAAiB,OAAO;4DAAI,OAAO;wDAAgB;wDAC5D;4DAAE,OAAO;4DAAU,OAAO;4DAAI,OAAO;wDAAiB;qDACvD,CAAC,GAAG,CAAC,CAAC,MAAM,kBACX,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAuB,KAAK,KAAK;;;;;;sFACjD,8OAAC;4EAAK,WAAU;;gFAAmC,KAAK,KAAK;gFAAC;;;;;;;;;;;;;8EAEhE,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,SAAS;4EAAE,OAAO;wEAAE;wEACpB,SAAS;4EAAE,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;wEAAC;wEACnC,YAAY;4EAAE,UAAU;4EAAG,OAAO,IAAI;wEAAI;wEAC1C,WAAW,CAAC,oBAAoB,EAAE,KAAK,KAAK,EAAE;;;;;;;;;;;;2DAV1C,KAAK,KAAK;;;;;;;;;;;;;;;;sDAkB1B,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAA+C;;;;;;8DAC7D,8OAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,SAAS;4DAAiB,YAAY;4DAAI,MAAM;wDAAO;wDACzD;4DAAE,SAAS;4DAAkB,YAAY;4DAAI,MAAM;wDAAO;wDAC1D;4DAAE,SAAS;4DAAW,YAAY;4DAAI,MAAM;wDAAO;wDACnD;4DAAE,SAAS;4DAAS,YAAY;4DAAI,MAAM;wDAAO;qDAClD,CAAC,GAAG,CAAC,CAAC,SAAS,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DAET,WAAU;4DACV,SAAS;gEAAE,SAAS;gEAAG,GAAG,CAAC;4DAAG;4DAC9B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,YAAY;gEAAE,OAAO,IAAI;4DAAI;4DAC7B,YAAY;gEAAE,GAAG;4DAAE;;8EAEnB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAW,QAAQ,IAAI;;;;;;sFACvC,8OAAC;4EAAK,WAAU;sFAA6B,QAAQ,OAAO;;;;;;;;;;;;8EAE9D,8OAAC;oEAAK,WAAU;;wEAAmC,QAAQ,UAAU;wEAAC;;;;;;;;2DAXjE,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAoBhC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAK,WAAU;sDAAO;;;;;;wCAAS;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EAAoB;;;;;;8EACpC,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EAAqB;;;;;;8EACrC,8OAAC;8EAAK;;;;;;;;;;;;sEAER,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;8EAAoB;;;;;;8EACpC,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAIZ,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;sEAEpD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,8OAAC;oEAAK,WAAU;8EAAkC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ5D,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAS,IAAM,cAAc;oCAAI,MAAK;8CAAK;;;;;;8CAGnD,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;8CAGpC,8OAAC,iIAAA,CAAA,UAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;YAM5C;gBACE,qBAAO,8OAAC;8BAAI;;;;;;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA+B;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;sCAKlE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDAAK;;;;;;sDACN,8OAAC;;gDAAM,gBAAgB,IAAI;gDAAC;gDAAE,OAAO,MAAM;gDAAC;;;;;;;;;;;;;8CAE9C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS;4CAAE,OAAO,GAAG,AAAC,gBAAgB,IAAI,GAAG,OAAO,MAAM,GAAI,IAAI,CAAC,CAAC;wCAAC;wCACrE,YAAY;4CAAE,UAAU;wCAAI;;;;;;;;;;;;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,YAAY;oDAAE,OAAO;gDAAK;gDAC1B,WAAW,CAAC,oDAAoD,EAC9D,iBAAiB,MAAM,EAAE,GACrB,4EACA,gBAAgB,GAAG,CAAC,MAAM,EAAE,IAC5B,2CACA,yDACJ;gDACF,SAAS,IAAM,gBAAgB,MAAM,EAAE;0DAEvC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAW,CAAC,wEAAwE,EACvF,gBAAgB,GAAG,CAAC,MAAM,EAAE,IACxB,+BACA,iBAAiB,MAAM,EAAE,GACzB,+BACA,iCACJ;sEACC,gBAAgB,GAAG,CAAC,MAAM,EAAE,IAAI,MAAM,MAAM,EAAE;;;;;;sEAEjD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA2C,MAAM,KAAK;;;;;;8EACpE,8OAAC;oEAAE,WAAU;8EAA+B,MAAM,WAAW;;;;;;;;;;;;;;;;;;+CAvB5D,MAAM,EAAE;;;;;;;;;;kDA8BnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,SAAS;4CAAW,WAAU;sDAAS;;;;;;;;;;;;;;;;;;;;;;sCAQjF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ;;;;;;8DAEH,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,kBAAkB;;;;;;sEAErB,8OAAC;4DAAE,WAAU;sEACV,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;kDAO3B,8OAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIH,8OAAC,yLAAA,CAAA,kBAAe;kDACb,oBAAoB,kCACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;4CAAE;4CACtB,SAAS;gDAAE,SAAS;4CAAE;4CACtB,MAAM;gDAAE,SAAS;4CAAE;4CACnB,WAAU;sDAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,OAAO;oDAAK,SAAS;gDAAE;gDAClC,SAAS;oDAAE,OAAO;oDAAG,SAAS;gDAAE;gDAChC,MAAM;oDAAE,OAAO;oDAAK,SAAS;gDAAE;gDAC/B,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,8OAAC;wDAAG,WAAU;kEAA2C;;;;;;kEACzD,8OAAC;wDAAE,WAAU;kEAA4B,iBAAiB,WAAW;;;;;;kEACrE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,UAAM;gEAAC,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;0EACnD,8OAAC,iIAAA,CAAA,UAAM;gEAAC,SAAQ;gEAAQ,SAAS,IAAM,oBAAoB;0EAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAhDxE;;;;;;;;;;;;;;;;8BA2DX,8OAAC,yLAAA,CAAA,kBAAe;8BACb,gBAAgB,IAAI,KAAK,OAAO,MAAM,kBACrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAM;wCAAC,MAAK;wCAAK,MAAK;kDAAU;;;;;;kDAGjC,8OAAC,iIAAA,CAAA,UAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,SAAS;kDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxE", "debugId": null}}]}